import { User, UserPlus, UserMinus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const UserPanel = () => {
  const users = [
    { id: 1, name: '<PERSON>', status: 'online', role: 'user' },
    { id: 2, name: '<PERSON>', status: 'offline', role: 'moderator' },
    { id: 3, name: '<PERSON>', status: 'online', role: 'user' },
  ];

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-green-400">
          <User className="h-5 w-5" />
          User Management
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* User Stats */}
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="bg-slate-700 p-2 rounded">
            <div className="text-green-400 font-medium">Online</div>
            <div className="text-white text-lg">2</div>
          </div>
          <div className="bg-slate-700 p-2 rounded">
            <div className="text-green-400 font-medium">Total</div>
            <div className="text-white text-lg">3</div>
          </div>
        </div>

        {/* User List */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">Active Users</h4>
          {users.map((user) => (
            <div key={user.id} className="flex items-center justify-between bg-slate-700 p-2 rounded text-sm">
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${user.status === 'online' ? 'bg-green-400' : 'bg-slate-500'}`} />
                <span className="text-white">{user.name}</span>
                <span className="text-slate-400 text-xs">({user.role})</span>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">Quick Actions</h4>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <UserPlus className="h-3 w-3" />
            Add User
          </button>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <UserMinus className="h-3 w-3" />
            Remove User
          </button>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserPanel;