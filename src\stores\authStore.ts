import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { AuthState, User, LoginCredentials, AuthResponse } from '@/types/auth';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  
  // Utilities
  hasPermission: (permission: string) => boolean;
  isRole: (role: string) => boolean;
}

// Mock API functions (replace with real API calls)
const mockLogin = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock validation
  if (credentials.username === 'admin' && credentials.password === 'admin') {
    return {
      user: {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
        createdAt: '2024-01-01T00:00:00Z',
        lastLogin: new Date().toISOString(),
        isActive: true,
      },
      token: 'mock-jwt-token-admin',
      refreshToken: 'mock-refresh-token',
      expiresIn: 3600,
    };
  } else if (credentials.username === 'dev' && credentials.password === 'dev') {
    return {
      user: {
        id: '2',
        username: 'dev',
        email: '<EMAIL>',
        role: 'developer',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=dev',
        createdAt: '2024-01-01T00:00:00Z',
        lastLogin: new Date().toISOString(),
        isActive: true,
      },
      token: 'mock-jwt-token-dev',
      refreshToken: 'mock-refresh-token',
      expiresIn: 3600,
    };
  } else {
    throw new Error('Invalid credentials');
  }
};

const mockRefreshToken = async (): Promise<AuthResponse> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  throw new Error('Token refresh not implemented');
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await mockLogin(credentials);
          
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      refreshToken: async () => {
        set({ isLoading: true });
        
        try {
          const response = await mockRefreshToken();
          
          set({
            user: response.user,
            token: response.token,
            isLoading: false,
          });
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // Utilities
      hasPermission: (permission: string) => {
        const user = get().user;
        if (!user) return false;
        
        // Simple role-based permissions
        const rolePermissions = {
          admin: ['*'], // Admin has all permissions
          developer: ['read', 'write', 'execute', 'terminal', 'logs'],
          viewer: ['read'],
        };
        
        const userPermissions = rolePermissions[user.role] || [];
        return userPermissions.includes('*') || userPermissions.includes(permission);
      },

      isRole: (role: string) => {
        const user = get().user;
        return user?.role === role;
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
