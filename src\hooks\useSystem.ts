import { useCallback, useEffect } from 'react';
import { useSystemStore } from '@/stores/systemStore';
import { systemApi } from '@/services/api';
import { wsService, startMockDataSimulation, stopMockDataSimulation } from '@/services/websocket';
import { toast } from '@/hooks/use-toast';
import type { SystemMetrics, ProcessInfo, ServiceInfo } from '@/types/system';

export const useSystem = () => {
  const {
    metrics,
    processes,
    services,
    logs,
    alerts,
    isConnected,
    lastUpdate,
    updateMetrics,
    updateProcesses,
    updateServices,
    addLogEntry,
    addAlert,
    acknowledgeAlert,
    clearLogs,
    setConnectionStatus,
    getProcessById,
    getServiceByName,
    getUnacknowledgedAlerts,
    getCriticalAlerts,
  } = useSystemStore();

  // Initialize real-time data connection
  useEffect(() => {
    const initializeConnection = async () => {
      try {
        // Try to connect to WebSocket
        await wsService.connect();
      } catch (error) {
        console.warn('WebSocket connection failed, using mock data:', error);
        // Fall back to mock data simulation
        startMockDataSimulation();
      }
    };

    initializeConnection();

    return () => {
      wsService.disconnect();
      stopMockDataSimulation();
    };
  }, []);

  // Fetch initial data
  const fetchSystemData = useCallback(async () => {
    try {
      const [metricsData, processesData, servicesData] = await Promise.allSettled([
        systemApi.getMetrics(),
        systemApi.getProcesses(),
        systemApi.getServices(),
      ]);

      if (metricsData.status === 'fulfilled') {
        updateMetrics(metricsData.value);
      }
      if (processesData.status === 'fulfilled') {
        updateProcesses(processesData.value);
      }
      if (servicesData.status === 'fulfilled') {
        updateServices(servicesData.value);
      }
    } catch (error) {
      console.error('Failed to fetch system data:', error);
      // Continue with mock data if API fails
    }
  }, [updateMetrics, updateProcesses, updateServices]);

  // Service management functions
  const startService = useCallback(async (serviceName: string) => {
    try {
      await systemApi.startService(serviceName);
      toast({
        title: 'Service Started',
        description: `${serviceName} has been started successfully`,
      });
      // Refresh services data
      const servicesData = await systemApi.getServices();
      updateServices(servicesData);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to start service';
      toast({
        title: 'Service Start Failed',
        description: message,
        variant: 'destructive',
      });
      throw error;
    }
  }, [updateServices]);

  const stopService = useCallback(async (serviceName: string) => {
    try {
      await systemApi.stopService(serviceName);
      toast({
        title: 'Service Stopped',
        description: `${serviceName} has been stopped successfully`,
      });
      // Refresh services data
      const servicesData = await systemApi.getServices();
      updateServices(servicesData);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to stop service';
      toast({
        title: 'Service Stop Failed',
        description: message,
        variant: 'destructive',
      });
      throw error;
    }
  }, [updateServices]);

  const restartService = useCallback(async (serviceName: string) => {
    try {
      await systemApi.restartService(serviceName);
      toast({
        title: 'Service Restarted',
        description: `${serviceName} has been restarted successfully`,
      });
      // Refresh services data
      const servicesData = await systemApi.getServices();
      updateServices(servicesData);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to restart service';
      toast({
        title: 'Service Restart Failed',
        description: message,
        variant: 'destructive',
      });
      throw error;
    }
  }, [updateServices]);

  const killProcess = useCallback(async (pid: number) => {
    try {
      await systemApi.killProcess(pid);
      toast({
        title: 'Process Terminated',
        description: `Process ${pid} has been terminated`,
      });
      // Refresh processes data
      const processesData = await systemApi.getProcesses();
      updateProcesses(processesData);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to kill process';
      toast({
        title: 'Process Termination Failed',
        description: message,
        variant: 'destructive',
      });
      throw error;
    }
  }, [updateProcesses]);

  // Alert management
  const acknowledgeAlertById = useCallback((alertId: string) => {
    acknowledgeAlert(alertId);
    toast({
      title: 'Alert Acknowledged',
      description: 'The alert has been marked as acknowledged',
    });
  }, [acknowledgeAlert]);

  // Utility functions
  const getSystemHealth = useCallback(() => {
    if (!metrics) return 'unknown';
    
    const cpuHealth = metrics.cpu.usage < 80 ? 'good' : metrics.cpu.usage < 90 ? 'warning' : 'critical';
    const memoryHealth = metrics.memory.percentage < 80 ? 'good' : metrics.memory.percentage < 90 ? 'warning' : 'critical';
    const diskHealth = metrics.disk.percentage < 80 ? 'good' : metrics.disk.percentage < 90 ? 'warning' : 'critical';
    
    const healthLevels = [cpuHealth, memoryHealth, diskHealth];
    
    if (healthLevels.includes('critical')) return 'critical';
    if (healthLevels.includes('warning')) return 'warning';
    return 'good';
  }, [metrics]);

  const getActiveProcessCount = useCallback(() => {
    return processes.filter(p => p.status === 'running').length;
  }, [processes]);

  const getActiveServiceCount = useCallback(() => {
    return services.filter(s => s.status === 'active').length;
  }, [services]);

  const getRecentLogs = useCallback((count: number = 10) => {
    return logs.slice(0, count);
  }, [logs]);

  return {
    // State
    metrics,
    processes,
    services,
    logs,
    alerts,
    isConnected,
    lastUpdate,
    
    // Actions
    fetchSystemData,
    startService,
    stopService,
    restartService,
    killProcess,
    acknowledgeAlert: acknowledgeAlertById,
    clearLogs,
    
    // Utilities
    getProcessById,
    getServiceByName,
    getUnacknowledgedAlerts,
    getCriticalAlerts,
    getSystemHealth,
    getActiveProcessCount,
    getActiveServiceCount,
    getRecentLogs,
  };
};
