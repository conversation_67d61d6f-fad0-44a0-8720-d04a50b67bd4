import { ArrowLeft, Shield, Users, Activity, Database, Settings, AlertTriangle, CheckCircle, Lock, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';

const AdminPage = () => {
  const systemOverview = [
    { label: 'Total Users', value: '1,247', change: '+12%', color: 'text-blue-400' },
    { label: 'Active Sessions', value: '89', change: '+5%', color: 'text-green-400' },
    { label: 'System Load', value: '34%', change: '-8%', color: 'text-yellow-400' },
    { label: 'Storage Used', value: '2.4TB', change: '+15%', color: 'text-purple-400' },
  ];

  const securityAlerts = [
    { type: 'info', message: 'System backup completed successfully', time: '2 min ago' },
    { type: 'warning', message: 'High CPU usage detected on server-02', time: '15 min ago' },
    { type: 'success', message: 'Security scan completed - no threats found', time: '1 hour ago' },
    { type: 'info', message: 'Database optimization scheduled for tonight', time: '3 hours ago' },
  ];

  const userActivity = [
    { user: 'john.doe', action: 'Terminal access', time: '2 min ago', status: 'active' },
    { user: 'jane.smith', action: 'Profile updated', time: '15 min ago', status: 'completed' },
    { user: 'admin', action: 'System backup', time: '1 hour ago', status: 'completed' },
    { user: 'bob.wilson', action: 'Failed login attempt', time: '2 hours ago', status: 'failed' },
  ];

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-400" />;
      default: return <Activity className="h-4 w-4 text-blue-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'completed': return 'text-blue-400';
      case 'failed': return 'text-red-400';
      default: return 'text-slate-400';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 text-white">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,_var(--tw-gradient-stops))] from-purple-900/20 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>
      
      <div className="relative container mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Link 
              to="/" 
              className="group flex items-center justify-center w-12 h-12 rounded-xl bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 hover:border-purple-400/50 hover:bg-slate-700/50 transition-all duration-300"
            >
              <ArrowLeft className="h-5 w-5 text-purple-400 group-hover:-translate-x-0.5 transition-transform duration-200" />
            </Link>
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="absolute inset-0 bg-purple-400/20 blur-xl rounded-full"></div>
                <Shield className="relative h-10 w-10 text-purple-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">Admin Panel</h1>
                <p className="text-slate-400">System management and oversight</p>
              </div>
            </div>
          </div>
        </div>

        {/* System Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {systemOverview.map((item, index) => (
            <Card key={index} className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-slate-400 text-sm">{item.label}</span>
                  <span className={`text-xs ${item.change.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                    {item.change}
                  </span>
                </div>
                <div className={`text-2xl font-bold ${item.color}`}>{item.value}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Security Alerts */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="bg-gradient-to-r from-purple-900/20 to-slate-900/20 border-b border-slate-700/50">
                <CardTitle className="flex items-center gap-3 text-purple-400">
                  <AlertTriangle className="h-5 w-5" />
                  Security & System Alerts
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-slate-700/50">
                  {securityAlerts.map((alert, index) => (
                    <div key={index} className="p-4 hover:bg-slate-800/30 transition-colors">
                      <div className="flex items-start gap-3">
                        {getAlertIcon(alert.type)}
                        <div className="flex-1">
                          <p className="text-white text-sm">{alert.message}</p>
                          <p className="text-slate-400 text-xs mt-1">{alert.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* User Activity Monitor */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="bg-gradient-to-r from-purple-900/20 to-slate-900/20 border-b border-slate-700/50">
                <CardTitle className="flex items-center gap-3 text-purple-400">
                  <Eye className="h-5 w-5" />
                  User Activity Monitor
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-slate-700/50">
                  {userActivity.map((activity, index) => (
                    <div key={index} className="p-4 hover:bg-slate-800/30 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center text-white text-xs font-medium">
                            {activity.user.split('.')[0][0].toUpperCase()}
                          </div>
                          <div>
                            <p className="text-white text-sm font-medium">{activity.user}</p>
                            <p className="text-slate-400 text-xs">{activity.action}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`text-sm font-medium ${getStatusColor(activity.status)}`}>{activity.status}</p>
                          <p className="text-slate-400 text-xs">{activity.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Admin Controls Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-purple-400 text-lg flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <button className="w-full text-left p-3 rounded-lg bg-purple-600 hover:bg-purple-500 transition-colors text-white text-sm flex items-center gap-3">
                  <Database className="h-4 w-4" />
                  System Backup
                </button>
                <button className="w-full text-left p-3 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-purple-400/30 transition-all text-white text-sm flex items-center gap-3">
                  <Users className="h-4 w-4" />
                  Manage Users
                </button>
                <button className="w-full text-left p-3 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-purple-400/30 transition-all text-white text-sm flex items-center gap-3">
                  <Lock className="h-4 w-4" />
                  Security Settings
                </button>
                <button className="w-full text-left p-3 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-purple-400/30 transition-all text-white text-sm flex items-center gap-3">
                  <Activity className="h-4 w-4" />
                  View Logs
                </button>
              </CardContent>
            </Card>

            {/* System Health */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-purple-400 text-lg flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  System Health
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 text-sm">Database</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-400"></div>
                      <span className="text-green-400 text-sm">Healthy</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 text-sm">API Services</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-400"></div>
                      <span className="text-green-400 text-sm">Online</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 text-sm">Backup System</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                      <span className="text-yellow-400 text-sm">Running</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 text-sm">Security Scan</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-400"></div>
                      <span className="text-green-400 text-sm">Clean</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Actions */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-purple-400 text-lg">Recent Admin Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm">
                  <p className="text-white">System backup initiated</p>
                  <p className="text-slate-400">2 minutes ago</p>
                </div>
                <div className="text-sm">
                  <p className="text-white">User permissions updated</p>
                  <p className="text-slate-400">1 hour ago</p>
                </div>
                <div className="text-sm">
                  <p className="text-white">Security scan completed</p>
                  <p className="text-slate-400">3 hours ago</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPage;