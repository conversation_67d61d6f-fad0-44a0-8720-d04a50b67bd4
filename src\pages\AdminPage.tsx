import { ArrowLeft, Shield } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import ProcessManager from '@/components/system/ProcessManager';
import ServiceManager from '@/components/system/ServiceManager';
import LogViewer from '@/components/system/LogViewer';

const AdminPage = () => {
  return (
    <div className="container mx-auto px-6 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Link
          to="/"
          className="group flex items-center justify-center w-12 h-12 rounded-xl bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 hover:border-purple-400/50 hover:bg-slate-700/50 transition-all duration-300"
        >
          <ArrowLeft className="h-5 w-5 text-purple-400 group-hover:-translate-x-0.5 transition-transform duration-200" />
        </Link>
        <div className="flex items-center gap-4">
          <div className="relative">
            <div className="absolute inset-0 bg-purple-400/20 blur-xl rounded-full"></div>
            <Shield className="relative h-10 w-10 text-purple-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Panel</h1>
            <p className="text-slate-400">Advanced system management and monitoring</p>
          </div>
        </div>
      </div>

      {/* System Management Components */}
      <div className="space-y-8">
        <ProcessManager />
        <ServiceManager />
        <LogViewer />
      </div>
    </div>
  );
};

export default AdminPage;