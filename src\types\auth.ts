// Authentication types
export interface User {
  id: string;
  username: string;
  email: string;
  role: UserR<PERSON>;
  avatar?: string;
  createdAt: string;
  lastLogin?: string;
  isActive: boolean;
}

export type UserRole = 'admin' | 'developer' | 'viewer';

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface TokenPayload {
  userId: string;
  username: string;
  role: UserRole;
  exp: number;
  iat: number;
}

// Permission types
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface RolePermissions {
  [key in UserRole]: Permission[];
}
