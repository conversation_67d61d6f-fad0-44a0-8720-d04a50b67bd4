import { Terminal, Users, Shield, ArrowRight, Code2, Zap, Lock, Activity, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import SystemMetricsChart from '@/components/charts/SystemMetricsChart';
import { useAuth } from '@/hooks/useAuth';
import { useSystem } from '@/hooks/useSystem';

const Index = () => {
  const { user } = useAuth();
  const { getSystemHealth, getActiveProcessCount, getActiveServiceCount, getUnacknowledgedAlerts } = useSystem();

  const systemHealth = getSystemHealth();
  const activeProcesses = getActiveProcessCount();
  const activeServices = getActiveServiceCount();
  const unacknowledgedAlerts = getUnacknowledgedAlerts();

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'good': return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'warning': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'critical': return 'text-red-400 bg-red-500/20 border-red-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const quickStats = [
    {
      icon: <Activity className="h-6 w-6 text-emerald-400" />,
      title: "System Health",
      value: systemHealth,
      color: getHealthColor(systemHealth),
      description: "Overall system status"
    },
    {
      icon: <Code2 className="h-6 w-6 text-blue-400" />,
      title: "Active Processes",
      value: activeProcesses.toString(),
      color: "text-blue-400 bg-blue-500/20 border-blue-500/30",
      description: "Running processes"
    },
    {
      icon: <Zap className="h-6 w-6 text-purple-400" />,
      title: "Active Services",
      value: activeServices.toString(),
      color: "text-purple-400 bg-purple-500/20 border-purple-500/30",
      description: "Running services"
    },
    {
      icon: <AlertTriangle className="h-6 w-6 text-orange-400" />,
      title: "Alerts",
      value: unacknowledgedAlerts.length.toString(),
      color: unacknowledgedAlerts.length > 0 ? "text-red-400 bg-red-500/20 border-red-500/30" : "text-green-400 bg-green-500/20 border-green-500/30",
      description: "Unacknowledged alerts"
    }
  ];

  return (
    <div className="container mx-auto px-6 py-8 space-y-8">
      {/* Welcome Section */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
          Welcome back, {user?.username}!
        </h1>
        <p className="text-xl text-slate-300 max-w-2xl mx-auto">
          Your command center dashboard with real-time system monitoring and management tools.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => (
          <Card key={index} className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  {stat.icon}
                  <span className="text-sm font-medium text-slate-300">{stat.title}</span>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-3xl font-bold text-white">{stat.value}</span>
                <Badge className={stat.color}>
                  {stat.description}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* System Metrics Chart */}
      <SystemMetricsChart />

      {/* Navigation Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {/* Terminal Page */}
          <Link to="/terminal" className="group">
            <Card className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-sm border-slate-700/50 hover:border-emerald-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-emerald-400/10 hover:-translate-y-2 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="pb-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-emerald-400/20 blur-xl rounded-full group-hover:bg-emerald-400/30 transition-colors"></div>
                    <Terminal className="relative h-10 w-10 text-emerald-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-500 group-hover:text-emerald-400 group-hover:translate-x-1 transition-all duration-300" />
                </div>
                <CardTitle className="text-2xl text-white group-hover:text-emerald-400 transition-colors duration-300">
                  Terminal Console
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <p className="text-slate-300 mb-6 leading-relaxed">
                  Advanced pseudo-terminal with command execution, history, and interactive shell environment.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-emerald-400"></div>
                    Interactive command line interface
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-emerald-400"></div>
                    Command history & autocomplete
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-emerald-400"></div>
                    Real-time system monitoring
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Users Page */}
          <Link to="/users" className="group">
            <Card className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-sm border-slate-700/50 hover:border-blue-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-blue-400/10 hover:-translate-y-2 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="pb-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-blue-400/20 blur-xl rounded-full group-hover:bg-blue-400/30 transition-colors"></div>
                    <Users className="relative h-10 w-10 text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-500 group-hover:text-blue-400 group-hover:translate-x-1 transition-all duration-300" />
                </div>
                <CardTitle className="text-2xl text-white group-hover:text-blue-400 transition-colors duration-300">
                  User Dashboard
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <p className="text-slate-300 mb-6 leading-relaxed">
                  Comprehensive user account management with profile settings and activity monitoring.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
                    Account profile management
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
                    Session & activity tracking
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
                    Preference customization
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Admin Page */}
          <Link to="/admin" className="group">
            <Card className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-sm border-slate-700/50 hover:border-purple-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-400/10 hover:-translate-y-2 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="pb-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-purple-400/20 blur-xl rounded-full group-hover:bg-purple-400/30 transition-colors"></div>
                    <Shield className="relative h-10 w-10 text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-500 group-hover:text-purple-400 group-hover:translate-x-1 transition-all duration-300" />
                </div>
                <CardTitle className="text-2xl text-white group-hover:text-purple-400 transition-colors duration-300">
                  Admin Panel
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <p className="text-slate-300 mb-6 leading-relaxed">
                  Minimal administrative control center with security management and system oversight.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400"></div>
                    System security controls
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400"></div>
                    User permission management
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400"></div>
                    Audit logs & monitoring
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Footer */}
        <div className="text-center mt-20">
          <div className="inline-flex items-center gap-2 bg-slate-800/30 backdrop-blur-sm border border-slate-700/30 rounded-full px-6 py-3">
            <div className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse"></div>
            <span className="text-slate-400 text-sm font-medium">Command Hub - Professional Development Suite</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;