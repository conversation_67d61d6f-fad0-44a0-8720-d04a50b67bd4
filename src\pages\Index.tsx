import { Terminal, Users, Shield, ArrowRight, Code2, Zap, Lock } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';

const Index = () => {
  const features = [
    {
      icon: <Code2 className="h-6 w-6 text-emerald-400" />,
      title: "Advanced Terminal",
      description: "Full-featured pseudo-terminal with command history and interactive shell capabilities."
    },
    {
      icon: <Zap className="h-6 w-6 text-blue-400" />,
      title: "Real-time Monitoring",
      description: "Live system stats and performance monitoring with instant updates."
    },
    {
      icon: <Lock className="h-6 w-6 text-purple-400" />,
      title: "Secure Administration",
      description: "Enterprise-grade security with role-based access control and audit trails."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 text-white overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-emerald-900/20 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>
      
      <div className="relative container mx-auto px-6 py-16">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <div className="flex items-center justify-center gap-4 mb-8">
            <div className="relative">
              <div className="absolute inset-0 bg-emerald-400/20 blur-2xl rounded-full"></div>
              <Terminal className="relative h-16 w-16 text-emerald-400" />
            </div>
            <h1 className="text-6xl font-bold bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
              Command Hub
            </h1>
          </div>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed mb-8">
            Professional command center with advanced terminal emulation, user management, and administrative controls. 
            Built for developers who demand precision and elegance.
          </p>
          
          {/* Feature Pills */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2 bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-full px-4 py-2">
                {feature.icon}
                <span className="text-sm font-medium text-slate-300">{feature.title}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {/* Terminal Page */}
          <Link to="/terminal" className="group">
            <Card className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-sm border-slate-700/50 hover:border-emerald-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-emerald-400/10 hover:-translate-y-2 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="pb-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-emerald-400/20 blur-xl rounded-full group-hover:bg-emerald-400/30 transition-colors"></div>
                    <Terminal className="relative h-10 w-10 text-emerald-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-500 group-hover:text-emerald-400 group-hover:translate-x-1 transition-all duration-300" />
                </div>
                <CardTitle className="text-2xl text-white group-hover:text-emerald-400 transition-colors duration-300">
                  Terminal Console
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <p className="text-slate-300 mb-6 leading-relaxed">
                  Advanced pseudo-terminal with command execution, history, and interactive shell environment.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-emerald-400"></div>
                    Interactive command line interface
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-emerald-400"></div>
                    Command history & autocomplete
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-emerald-400"></div>
                    Real-time system monitoring
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Users Page */}
          <Link to="/users" className="group">
            <Card className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-sm border-slate-700/50 hover:border-blue-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-blue-400/10 hover:-translate-y-2 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="pb-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-blue-400/20 blur-xl rounded-full group-hover:bg-blue-400/30 transition-colors"></div>
                    <Users className="relative h-10 w-10 text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-500 group-hover:text-blue-400 group-hover:translate-x-1 transition-all duration-300" />
                </div>
                <CardTitle className="text-2xl text-white group-hover:text-blue-400 transition-colors duration-300">
                  User Dashboard
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <p className="text-slate-300 mb-6 leading-relaxed">
                  Comprehensive user account management with profile settings and activity monitoring.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
                    Account profile management
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
                    Session & activity tracking
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
                    Preference customization
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Admin Page */}
          <Link to="/admin" className="group">
            <Card className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-sm border-slate-700/50 hover:border-purple-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-400/10 hover:-translate-y-2 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="pb-6 relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-purple-400/20 blur-xl rounded-full group-hover:bg-purple-400/30 transition-colors"></div>
                    <Shield className="relative h-10 w-10 text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-500 group-hover:text-purple-400 group-hover:translate-x-1 transition-all duration-300" />
                </div>
                <CardTitle className="text-2xl text-white group-hover:text-purple-400 transition-colors duration-300">
                  Admin Panel
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <p className="text-slate-300 mb-6 leading-relaxed">
                  Minimal administrative control center with security management and system oversight.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400"></div>
                    System security controls
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400"></div>
                    User permission management
                  </div>
                  <div className="flex items-center gap-3 text-sm text-slate-400">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400"></div>
                    Audit logs & monitoring
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Footer */}
        <div className="text-center mt-20">
          <div className="inline-flex items-center gap-2 bg-slate-800/30 backdrop-blur-sm border border-slate-700/30 rounded-full px-6 py-3">
            <div className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse"></div>
            <span className="text-slate-400 text-sm font-medium">Command Hub • Professional Development Suite</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;