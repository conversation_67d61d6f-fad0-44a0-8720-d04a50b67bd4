import type { ApiResponse, <PERSON>piError, ApiRequestConfig, PaginatedResponse } from '@/types/api';
import type { LoginCredentials, AuthResponse, User } from '@/types/auth';
import type { SystemMetrics, ProcessInfo, ServiceInfo, LogEntry } from '@/types/system';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
const DEFAULT_TIMEOUT = 10000;

class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string,
    config: ApiRequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      params,
      timeout = DEFAULT_TIMEOUT,
      retries = 0,
    } = config;

    const url = new URL(`${this.baseURL}${endpoint}`);
    
    // Add query parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    // Get auth token from localStorage
    const token = localStorage.getItem('auth-storage');
    const authData = token ? JSON.parse(token) : null;
    
    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers,
    };

    if (authData?.state?.token) {
      requestHeaders.Authorization = `Bearer ${authData.state.token}`;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url.toString(), {
        method,
        headers: requestHeaders,
        body: method !== 'GET' && config.body ? JSON.stringify(config.body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData: ApiError = await response.json().catch(() => ({
          success: false,
          error: {
            code: 'UNKNOWN_ERROR',
            message: `HTTP ${response.status}: ${response.statusText}`,
          },
          timestamp: new Date().toISOString(),
        }));
        
        throw new Error(errorData.error.message);
      }

      const data: ApiResponse<T> = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return this.request<T>(endpoint, { ...config, retries: retries - 1 });
      }
      
      throw error;
    }
  }

  // Authentication endpoints
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: credentials,
    });
    return response.data;
  }

  async logout(): Promise<void> {
    await this.request('/auth/logout', {
      method: 'POST',
    });
  }

  async refreshToken(): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/refresh', {
      method: 'POST',
    });
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.request<User>('/auth/me');
    return response.data;
  }

  // System endpoints
  async getSystemMetrics(): Promise<SystemMetrics> {
    const response = await this.request<SystemMetrics>('/system/metrics');
    return response.data;
  }

  async getProcesses(): Promise<ProcessInfo[]> {
    const response = await this.request<ProcessInfo[]>('/system/processes');
    return response.data;
  }

  async getServices(): Promise<ServiceInfo[]> {
    const response = await this.request<ServiceInfo[]>('/system/services');
    return response.data;
  }

  async getLogs(params?: {
    page?: number;
    limit?: number;
    level?: string;
    service?: string;
    search?: string;
  }): Promise<PaginatedResponse<LogEntry>> {
    const response = await this.request<PaginatedResponse<LogEntry>>('/system/logs', {
      params,
    });
    return response.data;
  }

  async startService(serviceName: string): Promise<void> {
    await this.request(`/system/services/${serviceName}/start`, {
      method: 'POST',
    });
  }

  async stopService(serviceName: string): Promise<void> {
    await this.request(`/system/services/${serviceName}/stop`, {
      method: 'POST',
    });
  }

  async restartService(serviceName: string): Promise<void> {
    await this.request(`/system/services/${serviceName}/restart`, {
      method: 'POST',
    });
  }

  async killProcess(pid: number): Promise<void> {
    await this.request(`/system/processes/${pid}`, {
      method: 'DELETE',
    });
  }

  // User management endpoints
  async getUsers(params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
  }): Promise<PaginatedResponse<User>> {
    const response = await this.request<PaginatedResponse<User>>('/users', {
      params,
    });
    return response.data;
  }

  async createUser(userData: Partial<User>): Promise<User> {
    const response = await this.request<User>('/users', {
      method: 'POST',
      body: userData,
    });
    return response.data;
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    const response = await this.request<User>(`/users/${userId}`, {
      method: 'PUT',
      body: userData,
    });
    return response.data;
  }

  async deleteUser(userId: string): Promise<void> {
    await this.request(`/users/${userId}`, {
      method: 'DELETE',
    });
  }
}

// Create and export API client instance
export const apiClient = new ApiClient();

// Export individual API functions for easier use
export const authApi = {
  login: (credentials: LoginCredentials) => apiClient.login(credentials),
  logout: () => apiClient.logout(),
  refreshToken: () => apiClient.refreshToken(),
  getCurrentUser: () => apiClient.getCurrentUser(),
};

export const systemApi = {
  getMetrics: () => apiClient.getSystemMetrics(),
  getProcesses: () => apiClient.getProcesses(),
  getServices: () => apiClient.getServices(),
  getLogs: (params?: any) => apiClient.getLogs(params),
  startService: (name: string) => apiClient.startService(name),
  stopService: (name: string) => apiClient.stopService(name),
  restartService: (name: string) => apiClient.restartService(name),
  killProcess: (pid: number) => apiClient.killProcess(pid),
};

export const userApi = {
  getUsers: (params?: any) => apiClient.getUsers(params),
  createUser: (userData: Partial<User>) => apiClient.createUser(userData),
  updateUser: (userId: string, userData: Partial<User>) => apiClient.updateUser(userId, userData),
  deleteUser: (userId: string) => apiClient.deleteUser(userId),
};
