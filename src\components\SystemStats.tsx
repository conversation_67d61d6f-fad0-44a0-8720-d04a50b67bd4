
import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Cpu, HardDrive, Activity, Wifi } from 'lucide-react';

const SystemStats = () => {
  const [stats, setStats] = useState({
    cpu: 34,
    memory: 68,
    disk: 45,
    network: 85
  });

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        cpu: Math.max(10, Math.min(90, prev.cpu + (Math.random() - 0.5) * 10)),
        memory: Math.max(20, Math.min(95, prev.memory + (Math.random() - 0.5) * 5)),
        disk: prev.disk, // Disk usage stays relatively stable
        network: Math.max(0, Math.min(100, prev.network + (Math.random() - 0.5) * 20))
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (value: number) => {
    if (value < 50) return 'text-green-400';
    if (value < 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="space-y-4">
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-green-400">
            <Activity className="h-5 w-5" />
            System Stats
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* CPU Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Cpu className="h-4 w-4 text-blue-400" />
                <span className="text-white text-sm">CPU</span>
              </div>
              <span className={`text-sm font-mono ${getStatusColor(stats.cpu)}`}>
                {stats.cpu.toFixed(1)}%
              </span>
            </div>
            <Progress value={stats.cpu} className="h-2" />
          </div>

          {/* Memory Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-purple-400" />
                <span className="text-white text-sm">Memory</span>
              </div>
              <span className={`text-sm font-mono ${getStatusColor(stats.memory)}`}>
                {stats.memory.toFixed(1)}%
              </span>
            </div>
            <Progress value={stats.memory} className="h-2" />
          </div>

          {/* Disk Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <HardDrive className="h-4 w-4 text-orange-400" />
                <span className="text-white text-sm">Disk</span>
              </div>
              <span className={`text-sm font-mono ${getStatusColor(stats.disk)}`}>
                {stats.disk.toFixed(1)}%
              </span>
            </div>
            <Progress value={stats.disk} className="h-2" />
          </div>

          {/* Network Activity */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Wifi className="h-4 w-4 text-cyan-400" />
                <span className="text-white text-sm">Network</span>
              </div>
              <span className={`text-sm font-mono ${getStatusColor(stats.network)}`}>
                {stats.network.toFixed(1)}%
              </span>
            </div>
            <Progress value={stats.network} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Status Indicators */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="pb-3">
          <CardTitle className="text-green-400 text-sm">System Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-white text-sm">Database</span>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-400"></div>
              <span className="text-green-400 text-xs">Online</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-white text-sm">API Server</span>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-400"></div>
              <span className="text-green-400 text-xs">Online</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-white text-sm">Cache</span>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
              <span className="text-yellow-400 text-xs">Warning</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-white text-sm">Backup</span>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-400"></div>
              <span className="text-green-400 text-xs">Ready</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemStats;