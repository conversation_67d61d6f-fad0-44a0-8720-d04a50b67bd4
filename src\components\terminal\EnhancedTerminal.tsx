import { useState, useRef, useEffect } from 'react';
import { 
  Terminal, 
  Copy, 
  Download, 
  Trash2, 
  History, 
  Settings, 
  Maximize2,
  ChevronRight,
  Clock,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/hooks/useAuth';
import { useSystem } from '@/hooks/useSystem';

interface Command {
  input: string;
  output: string[];
  timestamp: Date;
  exitCode: number;
}

interface TerminalSession {
  id: string;
  name: string;
  commands: Command[];
  isActive: boolean;
}

const EnhancedTerminal = () => {
  const { user } = useAuth();
  const { metrics, processes, services } = useSystem();
  const [sessions, setSessions] = useState<TerminalSession[]>([
    { id: '1', name: 'Main', commands: [], isActive: true }
  ]);
  const [activeSessionId, setActiveSessionId] = useState('1');
  const [currentInput, setCurrentInput] = useState('');
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const terminalRef = useRef<HTMLDivElement>(null);

  const activeSession = sessions.find(s => s.id === activeSessionId);

  const initialCommands: Command[] = [
    {
      input: 'welcome',
      output: [
        `Welcome to Command Console Dashboard v2.0.0`,
        `Logged in as: ${user?.username || 'user'} (${user?.role || 'user'})`,
        `Session started at: ${new Date().toLocaleString()}`,
        'Type "help" for available commands.',
        ''
      ],
      timestamp: new Date(),
      exitCode: 0
    }
  ];

  useEffect(() => {
    setSessions(prev => prev.map(session => 
      session.id === activeSessionId 
        ? { ...session, commands: initialCommands }
        : session
    ));
  }, [activeSessionId]);

  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [activeSession?.commands]);

  const executeCommand = (input: string): { output: string[]; exitCode: number } => {
    const cmd = input.trim().toLowerCase();
    const args = cmd.split(' ').slice(1);
    const command = cmd.split(' ')[0];

    switch (command) {
      case 'help':
        return {
          output: [
            'Available Commands:',
            '',
            'System Information:',
            '  help                 - Show this help message',
            '  whoami              - Show current user information',
            '  date                - Show current date and time',
            '  uptime              - Show system uptime',
            '  uname               - Show system information',
            '',
            'System Monitoring:',
            '  ps                  - Show running processes',
            '  top                 - Show system resources',
            '  services            - Show system services',
            '  logs [service]      - Show recent logs',
            '  status              - Show system status',
            '',
            'File Operations:',
            '  ls [path]           - List directory contents',
            '  pwd                 - Show current directory',
            '  cat <file>          - Display file contents',
            '',
            'Network:',
            '  ping <host>         - Ping a host',
            '  netstat             - Show network connections',
            '',
            'Utilities:',
            '  echo <message>      - Echo a message',
            '  calc <expression>   - Calculate mathematical expression',
            '  history             - Show command history',
            '  clear               - Clear terminal history',
            '  exit                - Exit current session',
            '',
            'Advanced:',
            '  sudo <command>      - Execute command with elevated privileges',
            '  systemctl <action> <service> - Control system services',
            ''
          ],
          exitCode: 0
        };

      case 'clear':
        setSessions(prev => prev.map(session => 
          session.id === activeSessionId 
            ? { ...session, commands: [] }
            : session
        ));
        return { output: [], exitCode: 0 };

      case 'whoami':
        return {
          output: [
            `Username: ${user?.username || 'unknown'}`,
            `Email: ${user?.email || 'unknown'}`,
            `Role: ${user?.role || 'unknown'}`,
            `Active: ${user?.isActive ? 'Yes' : 'No'}`,
            `Last Login: ${user?.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never'}`,
            ''
          ],
          exitCode: 0
        };

      case 'date':
        return {
          output: [new Date().toString(), ''],
          exitCode: 0
        };

      case 'uptime':
        const uptime = metrics?.uptime ? 
          new Date(Date.now() - metrics.uptime).toLocaleString() : 
          'Unknown';
        return {
          output: [`System started: ${uptime}`, ''],
          exitCode: 0
        };

      case 'uname':
        return {
          output: [
            'Command Console Dashboard v2.0.0',
            'Built with React + TypeScript',
            'Platform: Web Browser',
            ''
          ],
          exitCode: 0
        };

      case 'ps':
        if (!processes.length) {
          return {
            output: ['No process information available', ''],
            exitCode: 1
          };
        }
        const processLines = [
          'PID    NAME              CPU    MEM    STATUS   USER',
          '----------------------------------------------------'
        ];
        processes.slice(0, 10).forEach(proc => {
          processLines.push(
            `${proc.pid.toString().padEnd(6)} ${proc.name.padEnd(16)} ${proc.cpu.toFixed(1).padEnd(6)}% ${proc.memory.toFixed(1).padEnd(6)}% ${proc.status.padEnd(8)} ${proc.user}`
          );
        });
        return {
          output: [...processLines, ''],
          exitCode: 0
        };

      case 'top':
        if (!metrics) {
          return {
            output: ['System metrics not available', ''],
            exitCode: 1
          };
        }
        return {
          output: [
            'System Resources:',
            `CPU Usage: ${metrics.cpu.usage.toFixed(1)}% (${metrics.cpu.cores} cores)`,
            `Memory Usage: ${metrics.memory.percentage.toFixed(1)}% (${(metrics.memory.used/1024).toFixed(1)}GB / ${(metrics.memory.total/1024).toFixed(1)}GB)`,
            `Disk Usage: ${metrics.disk.percentage.toFixed(1)}% (${metrics.disk.used}GB / ${metrics.disk.total}GB)`,
            `Network: ↓${metrics.network.downloadSpeed.toFixed(1)} Mbps ↑${metrics.network.uploadSpeed.toFixed(1)} Mbps`,
            `Load Average: ${metrics.loadAverage.map(l => l.toFixed(2)).join(', ')}`,
            ''
          ],
          exitCode: 0
        };

      case 'services':
        if (!services.length) {
          return {
            output: ['No service information available', ''],
            exitCode: 1
          };
        }
        const serviceLines = [
          'SERVICE       STATUS    PORT   UPTIME',
          '------------------------------------'
        ];
        services.forEach(service => {
          const uptime = service.uptime ? `${Math.floor(service.uptime/3600)}h` : 'N/A';
          const port = service.port ? `:${service.port}` : 'N/A';
          serviceLines.push(
            `${service.name.padEnd(12)} ${service.status.padEnd(8)} ${port.padEnd(6)} ${uptime}`
          );
        });
        return {
          output: [...serviceLines, ''],
          exitCode: 0
        };

      case 'status':
        const health = metrics ? 
          (metrics.cpu.usage < 80 && metrics.memory.percentage < 80 && metrics.disk.percentage < 80) ? 
          'Healthy' : 'Warning' : 'Unknown';
        return {
          output: [
            'System Status Overview:',
            `Overall Health: ${health}`,
            `Active Processes: ${processes.filter(p => p.status === 'running').length}`,
            `Active Services: ${services.filter(s => s.status === 'active').length}`,
            `Connection Status: Connected`,
            ''
          ],
          exitCode: 0
        };

      case 'history':
        return {
          output: [
            'Command History:',
            ...commandHistory.map((cmd, i) => `${(i + 1).toString().padStart(4)}: ${cmd}`),
            ''
          ],
          exitCode: 0
        };

      case 'ls':
        const path = args[0] || '~';
        return {
          output: [
            `Contents of ${path}:`,
            'drwxr-xr-x  4 <USER> <GROUP>  4096 Dec 15 10:30 Documents',
            'drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 15 09:15 Downloads',
            'drwxr-xr-x  3 <USER> <GROUP>  4096 Dec 14 16:45 Projects',
            'drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 15 08:00 Scripts',
            '-rw-r--r--  1 <USER> <GROUP>  1024 Dec 15 11:20 readme.txt',
            '-rw-r--r--  1 <USER> <GROUP>   512 Dec 14 14:30 config.json',
            '-rw-r--r--  1 <USER> <GROUP>  2048 Dec 15 09:45 system.log',
            ''
          ],
          exitCode: 0
        };

      case 'pwd':
        return {
          output: ['/home/<USER>/dashboard', ''],
          exitCode: 0
        };

      case 'ping':
        const host = args[0] || 'localhost';
        return {
          output: [
            `PING ${host} (127.0.0.1): 56 data bytes`,
            `64 bytes from ${host}: icmp_seq=0 time=0.123ms`,
            `64 bytes from ${host}: icmp_seq=1 time=0.089ms`,
            `64 bytes from ${host}: icmp_seq=2 time=0.156ms`,
            `--- ${host} ping statistics ---`,
            '3 packets transmitted, 3 received, 0% packet loss',
            ''
          ],
          exitCode: 0
        };

      case 'echo':
        return {
          output: [args.join(' '), ''],
          exitCode: 0
        };

      case 'calc':
        try {
          const expression = args.join(' ');
          if (!expression) {
            return {
              output: ['Usage: calc <expression>', 'Example: calc 2 + 2', ''],
              exitCode: 1
            };
          }
          const sanitized = expression.replace(/[^0-9+\-*/.() ]/g, '');
          const result = new Function('return ' + sanitized)();
          return {
            output: [`${expression} = ${result}`, ''],
            exitCode: 0
          };
        } catch {
          return {
            output: ['Error: Invalid mathematical expression', ''],
            exitCode: 1
          };
        }

      case 'sudo':
        if (user?.role !== 'admin') {
          return {
            output: ['sudo: permission denied. Admin role required.', ''],
            exitCode: 1
          };
        }
        return {
          output: ['[sudo] Elevated privileges granted', ''],
          exitCode: 0
        };

      case 'exit':
        return {
          output: ['Session terminated.', ''],
          exitCode: 0
        };

      default:
        if (cmd === '') return { output: [''], exitCode: 0 };
        return {
          output: [`Command not found: ${command}`, 'Type "help" for available commands.', ''],
          exitCode: 127
        };
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentInput.trim()) return;

    const { output, exitCode } = executeCommand(currentInput);
    const newCommand: Command = {
      input: currentInput,
      output,
      timestamp: new Date(),
      exitCode
    };

    if (currentInput.trim().toLowerCase() !== 'clear') {
      setSessions(prev => prev.map(session => 
        session.id === activeSessionId 
          ? { ...session, commands: [...session.commands, newCommand] }
          : session
      ));
    }

    setCommandHistory(prev => [...prev, currentInput]);
    setCurrentInput('');
    setHistoryIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setCurrentInput(commandHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1);
          setCurrentInput('');
        } else {
          setHistoryIndex(newIndex);
          setCurrentInput(commandHistory[newIndex]);
        }
      }
    }
  };

  const exportSession = () => {
    if (!activeSession) return;
    
    const sessionData = {
      name: activeSession.name,
      timestamp: new Date().toISOString(),
      commands: activeSession.commands.map(cmd => ({
        input: cmd.input,
        output: cmd.output,
        timestamp: cmd.timestamp.toISOString(),
        exitCode: cmd.exitCode
      }))
    };

    const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `terminal-session-${activeSession.name}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearSession = () => {
    setSessions(prev => prev.map(session => 
      session.id === activeSessionId 
        ? { ...session, commands: [] }
        : session
    ));
  };

  return (
    <Card className={`bg-slate-800/50 border-slate-700 ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Terminal className="h-5 w-5 text-emerald-400" />
            <CardTitle className="text-lg font-semibold text-white">Enhanced Terminal</CardTitle>
            <Badge variant="outline" className="border-emerald-500/30 text-emerald-400">
              {activeSession?.name || 'Main'}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                <DropdownMenuItem onClick={exportSession} className="text-slate-300 hover:text-white hover:bg-slate-700">
                  <Download className="mr-2 h-4 w-4" />
                  Export Session
                </DropdownMenuItem>
                <DropdownMenuItem onClick={clearSession} className="text-slate-300 hover:text-white hover:bg-slate-700">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Clear Session
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-slate-700" />
                <DropdownMenuItem 
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="text-slate-300 hover:text-white hover:bg-slate-700"
                >
                  <Maximize2 className="mr-2 h-4 w-4" />
                  {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="bg-black text-green-400 font-mono text-sm">
          <div
            ref={terminalRef}
            className={`overflow-y-auto p-4 space-y-1 ${isFullscreen ? 'h-[calc(100vh-12rem)]' : 'h-96'}`}
            onClick={() => inputRef.current?.focus()}
          >
            {activeSession?.commands.map((command, index) => (
              <div key={index}>
                <div className="flex items-center gap-2">
                  <span className="text-blue-400">{user?.username || 'user'}@dashboard:~$</span>
                  <span className="text-white">{command.input}</span>
                  {command.exitCode !== 0 && (
                    <Badge variant="destructive" className="text-xs ml-2">
                      Exit {command.exitCode}
                    </Badge>
                  )}
                </div>
                {command.output.map((line, lineIndex) => (
                  <div key={lineIndex} className="text-green-400 ml-4">
                    {line}
                  </div>
                ))}
              </div>
            ))}

            {/* Input Line */}
            <form onSubmit={handleSubmit} className="flex items-center gap-2">
              <span className="text-blue-400">{user?.username || 'user'}@dashboard:~$</span>
              <input
                ref={inputRef}
                type="text"
                value={currentInput}
                onChange={(e) => setCurrentInput(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1 bg-transparent border-none outline-none text-white"
                autoFocus
                spellCheck={false}
              />
            </form>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedTerminal;
