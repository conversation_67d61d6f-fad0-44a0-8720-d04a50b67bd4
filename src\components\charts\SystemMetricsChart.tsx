import { useEffect, useState } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSystem } from '@/hooks/useSystem';
import { Cpu, MemoryStick, HardDrive, Network, Activity } from 'lucide-react';

interface MetricDataPoint {
  timestamp: string;
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}

const SystemMetricsChart = () => {
  const { metrics, isConnected } = useSystem();
  const [historicalData, setHistoricalData] = useState<MetricDataPoint[]>([]);

  // Store historical data for charts
  useEffect(() => {
    if (metrics) {
      const dataPoint: MetricDataPoint = {
        timestamp: new Date().toLocaleTimeString(),
        cpu: metrics.cpu.usage,
        memory: metrics.memory.percentage,
        disk: metrics.disk.percentage,
        network: metrics.network.downloadSpeed / 10, // Scale down for visualization
      };

      setHistoricalData(prev => {
        const newData = [...prev, dataPoint];
        // Keep only last 20 data points
        return newData.slice(-20);
      });
    }
  }, [metrics]);

  const getStatusColor = (value: number, type: 'usage' | 'speed' = 'usage') => {
    if (type === 'usage') {
      if (value >= 90) return 'text-red-400';
      if (value >= 70) return 'text-yellow-400';
      return 'text-green-400';
    }
    return 'text-blue-400';
  };

  const getStatusBadge = (value: number) => {
    if (value >= 90) return <Badge variant="destructive">Critical</Badge>;
    if (value >= 70) return <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400">Warning</Badge>;
    return <Badge variant="secondary" className="bg-green-500/20 text-green-400">Good</Badge>;
  };

  if (!metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-slate-700 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-slate-700 rounded w-1/2 mb-4"></div>
                <div className="h-20 bg-slate-700 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Indicator */}
      <div className="flex items-center gap-2">
        <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
        <span className="text-sm text-slate-400">
          {isConnected ? 'Real-time monitoring active' : 'Disconnected - showing cached data'}
        </span>
      </div>

      {/* Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* CPU Usage */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">CPU Usage</CardTitle>
            <Cpu className="h-4 w-4 text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <div className={`text-2xl font-bold ${getStatusColor(metrics.cpu.usage)}`}>
                {metrics.cpu.usage.toFixed(1)}%
              </div>
              {getStatusBadge(metrics.cpu.usage)}
            </div>
            <p className="text-xs text-slate-400">
              {metrics.cpu.cores} cores • {metrics.cpu.frequency?.toFixed(0)} MHz
            </p>
            <div className="mt-4 h-16">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={historicalData}>
                  <Area
                    type="monotone"
                    dataKey="cpu"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.2}
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Memory Usage */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Memory Usage</CardTitle>
            <MemoryStick className="h-4 w-4 text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <div className={`text-2xl font-bold ${getStatusColor(metrics.memory.percentage)}`}>
                {metrics.memory.percentage.toFixed(1)}%
              </div>
              {getStatusBadge(metrics.memory.percentage)}
            </div>
            <p className="text-xs text-slate-400">
              {(metrics.memory.used / 1024).toFixed(1)} GB / {(metrics.memory.total / 1024).toFixed(1)} GB
            </p>
            <div className="mt-4 h-16">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={historicalData}>
                  <Area
                    type="monotone"
                    dataKey="memory"
                    stroke="#10b981"
                    fill="#10b981"
                    fillOpacity={0.2}
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Disk Usage */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Disk Usage</CardTitle>
            <HardDrive className="h-4 w-4 text-purple-400" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <div className={`text-2xl font-bold ${getStatusColor(metrics.disk.percentage)}`}>
                {metrics.disk.percentage.toFixed(1)}%
              </div>
              {getStatusBadge(metrics.disk.percentage)}
            </div>
            <p className="text-xs text-slate-400">
              {metrics.disk.used} GB / {metrics.disk.total} GB
            </p>
            <div className="mt-4 h-16">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={historicalData}>
                  <Area
                    type="monotone"
                    dataKey="disk"
                    stroke="#8b5cf6"
                    fill="#8b5cf6"
                    fillOpacity={0.2}
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Network Activity */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Network</CardTitle>
            <Network className="h-4 w-4 text-orange-400" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <div className={`text-2xl font-bold ${getStatusColor(metrics.network.downloadSpeed, 'speed')}`}>
                {metrics.network.downloadSpeed.toFixed(0)}
              </div>
              <Badge variant="secondary" className="bg-orange-500/20 text-orange-400">
                Mbps
              </Badge>
            </div>
            <p className="text-xs text-slate-400">
              Down: {metrics.network.downloadSpeed.toFixed(1)} Mbps • Up: {metrics.network.uploadSpeed.toFixed(1)} Mbps
            </p>
            <div className="mt-4 h-16">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={historicalData}>
                  <Area
                    type="monotone"
                    dataKey="network"
                    stroke="#f97316"
                    fill="#f97316"
                    fillOpacity={0.2}
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Combined Chart */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <Activity className="h-5 w-5 text-emerald-400" />
            System Performance Overview
          </CardTitle>
          <CardDescription className="text-slate-400">
            Real-time system metrics over the last few minutes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={historicalData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis
                  dataKey="timestamp"
                  stroke="#9ca3af"
                  fontSize={12}
                />
                <YAxis
                  stroke="#9ca3af"
                  fontSize={12}
                  domain={[0, 100]}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1f2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#f3f4f6'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="cpu"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  name="CPU %"
                  dot={false}
                />
                <Line
                  type="monotone"
                  dataKey="memory"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Memory %"
                  dot={false}
                />
                <Line
                  type="monotone"
                  dataKey="disk"
                  stroke="#8b5cf6"
                  strokeWidth={2}
                  name="Disk %"
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemMetricsChart;
