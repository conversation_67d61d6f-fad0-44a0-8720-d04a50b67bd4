import { useState } from 'react';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Search, 
  Filter, 
  MoreHorizontal,
  AlertTriangle,
  CheckCircle,
  Clock,
  Skull
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useSystem } from '@/hooks/useSystem';
import { useAuth } from '@/hooks/useAuth';
import type { ProcessInfo } from '@/types/system';

const ProcessManager = () => {
  const { processes, killProcess } = useSystem();
  const { checkPermission } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<keyof ProcessInfo>('cpu');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const canManageProcesses = checkPermission('execute');

  const getStatusIcon = (status: ProcessInfo['status']) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'sleeping':
        return <Clock className="h-4 w-4 text-blue-400" />;
      case 'stopped':
        return <Square className="h-4 w-4 text-gray-400" />;
      case 'zombie':
        return <Skull className="h-4 w-4 text-red-400" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
    }
  };

  const getStatusBadge = (status: ProcessInfo['status']) => {
    const variants = {
      running: 'bg-green-500/20 text-green-400 border-green-500/30',
      sleeping: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      stopped: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
      zombie: 'bg-red-500/20 text-red-400 border-red-500/30',
    };

    return (
      <Badge className={variants[status] || variants.stopped}>
        {status}
      </Badge>
    );
  };

  const filteredAndSortedProcesses = processes
    .filter(process => {
      const matchesSearch = process.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           process.command.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           process.user.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || process.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
      }
      
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      
      if (sortOrder === 'desc') {
        return bStr.localeCompare(aStr);
      }
      return aStr.localeCompare(bStr);
    });

  const handleKillProcess = async (pid: number, name: string) => {
    if (!canManageProcesses) return;
    
    if (confirm(`Are you sure you want to terminate process "${name}" (PID: ${pid})?`)) {
      try {
        await killProcess(pid);
      } catch (error) {
        console.error('Failed to kill process:', error);
      }
    }
  };

  const handleSort = (column: keyof ProcessInfo) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  const formatUptime = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    }
    return `${diffMinutes}m`;
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white">Process Manager</CardTitle>
        <CardDescription className="text-slate-400">
          Monitor and manage running processes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search processes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-slate-700/50 border-slate-600 text-white"
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-40 bg-slate-700/50 border-slate-600 text-white">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="running">Running</SelectItem>
              <SelectItem value="sleeping">Sleeping</SelectItem>
              <SelectItem value="stopped">Stopped</SelectItem>
              <SelectItem value="zombie">Zombie</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Process Table */}
        <div className="rounded-lg border border-slate-700 overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="border-slate-700 hover:bg-slate-700/30">
                <TableHead 
                  className="text-slate-300 cursor-pointer hover:text-white"
                  onClick={() => handleSort('pid')}
                >
                  PID {sortBy === 'pid' && (sortOrder === 'desc' ? '↓' : '↑')}
                </TableHead>
                <TableHead 
                  className="text-slate-300 cursor-pointer hover:text-white"
                  onClick={() => handleSort('name')}
                >
                  Name {sortBy === 'name' && (sortOrder === 'desc' ? '↓' : '↑')}
                </TableHead>
                <TableHead className="text-slate-300">Status</TableHead>
                <TableHead 
                  className="text-slate-300 cursor-pointer hover:text-white"
                  onClick={() => handleSort('cpu')}
                >
                  CPU % {sortBy === 'cpu' && (sortOrder === 'desc' ? '↓' : '↑')}
                </TableHead>
                <TableHead 
                  className="text-slate-300 cursor-pointer hover:text-white"
                  onClick={() => handleSort('memory')}
                >
                  Memory % {sortBy === 'memory' && (sortOrder === 'desc' ? '↓' : '↑')}
                </TableHead>
                <TableHead 
                  className="text-slate-300 cursor-pointer hover:text-white"
                  onClick={() => handleSort('user')}
                >
                  User {sortBy === 'user' && (sortOrder === 'desc' ? '↓' : '↑')}
                </TableHead>
                <TableHead className="text-slate-300">Uptime</TableHead>
                {canManageProcesses && <TableHead className="text-slate-300">Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedProcesses.map((process) => (
                <TableRow key={process.pid} className="border-slate-700 hover:bg-slate-700/20">
                  <TableCell className="text-slate-300 font-mono">
                    {process.pid}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="text-white font-medium">{process.name}</span>
                      <span className="text-xs text-slate-400 truncate max-w-48">
                        {process.command}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(process.status)}
                      {getStatusBadge(process.status)}
                    </div>
                  </TableCell>
                  <TableCell className="text-slate-300">
                    <div className="flex items-center gap-2">
                      <span>{process.cpu.toFixed(1)}%</span>
                      <div className="w-16 h-2 bg-slate-700 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-400 transition-all duration-300"
                          style={{ width: `${Math.min(process.cpu, 100)}%` }}
                        />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-slate-300">
                    <div className="flex items-center gap-2">
                      <span>{process.memory.toFixed(1)}%</span>
                      <div className="w-16 h-2 bg-slate-700 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-green-400 transition-all duration-300"
                          style={{ width: `${Math.min(process.memory, 100)}%` }}
                        />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-slate-300">{process.user}</TableCell>
                  <TableCell className="text-slate-400 text-sm">
                    {formatUptime(process.startTime)}
                  </TableCell>
                  {canManageProcesses && (
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                          <DropdownMenuItem
                            onClick={() => handleKillProcess(process.pid, process.name)}
                            className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                          >
                            <Skull className="mr-2 h-4 w-4" />
                            Terminate
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {filteredAndSortedProcesses.length === 0 && (
          <div className="text-center py-8 text-slate-400">
            No processes found matching your criteria.
          </div>
        )}

        {/* Summary */}
        <div className="flex flex-wrap gap-4 text-sm text-slate-400">
          <span>Total: {processes.length} processes</span>
          <span>Running: {processes.filter(p => p.status === 'running').length}</span>
          <span>Sleeping: {processes.filter(p => p.status === 'sleeping').length}</span>
          <span>Stopped: {processes.filter(p => p.status === 'stopped').length}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProcessManager;
