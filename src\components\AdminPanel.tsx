
import { Settings, Lock, Database, FileText } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const AdminPanel = () => {
  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-green-400">
          <Settings className="h-5 w-5" />
          Admin Control
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Security Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">Security</h4>
          <div className="bg-slate-700 p-2 rounded text-sm">
            <div className="flex items-center justify-between mb-1">
              <span className="text-slate-400">Firewall:</span>
              <span className="text-green-400">Active</span>
            </div>
            <div className="flex items-center justify-between mb-1">
              <span className="text-slate-400">SSL:</span>
              <span className="text-green-400">Enabled</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-slate-400">Auth:</span>
              <span className="text-green-400">2FA</span>
            </div>
          </div>
        </div>

        {/* Database Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">Database</h4>
          <div className="bg-slate-700 p-2 rounded text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">Status:</span>
              <span className="text-green-400">Connected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Size:</span>
              <span className="text-white">2.4 GB</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Tables:</span>
              <span className="text-white">12</span>
            </div>
          </div>
        </div>

        {/* Admin Actions */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">Administration</h4>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <Lock className="h-3 w-3" />
            Security Settings
          </button>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <Database className="h-3 w-3" />
            Database Backup
          </button>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <FileText className="h-3 w-3" />
            System Logs
          </button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminPanel;
