import { useState, useRef, useEffect } from 'react';
import { ChevronRight } from 'lucide-react';

interface Command {
  input: string;
  output: string[];
  timestamp: Date;
}

const TerminalEmulator = () => {
  const [history, setHistory] = useState<Command[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const terminalRef = useRef<HTMLDivElement>(null);

  const initialCommands: Command[] = [
    {
      input: 'welcome',
      output: [
        'Welcome to Command Console Dashboard v1.0.0',
        'Type "help" for available commands.',
        ''
      ],
      timestamp: new Date()
    }
  ];

  useEffect(() => {
    setHistory(initialCommands);
  }, []);

  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [history]);

  const executeCommand = (input: string): string[] => {
    const cmd = input.trim().toLowerCase();
    const args = cmd.split(' ').slice(1);

    switch (cmd.split(' ')[0]) {
      case 'help':
        return [
          'Available Commands:',
          '  help                 - Show this help message',
          '  clear               - Clear terminal history',
          '  ls                  - List directory contents',
          '  pwd                 - Show current directory',
          '  whoami              - Show current user',
          '  date                - Show current date and time',
          '  uptime              - Show system uptime',
          '  ps                  - Show running processes',
          '  top                 - Show system resources',
          '  ping <host>         - Ping a host',
          '  echo <message>      - Echo a message',
          '  calc <expression>   - Calculate mathematical expression',
          '  weather             - Show weather information',
          '  about               - About this terminal',
          ''
        ];

      case 'clear':
        setHistory([]);
        return [];

      case 'ls':
        return [
          'drwxr-xr-x  4 <USER> <GROUP>  4096 Dec 15 10:30 Documents',
          'drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 15 09:15 Downloads',
          'drwxr-xr-x  3 <USER> <GROUP>  4096 Dec 14 16:45 Projects',
          '-rw-r--r--  1 <USER> <GROUP>  1024 Dec 15 11:20 readme.txt',
          '-rw-r--r--  1 <USER> <GROUP>   512 Dec 14 14:30 config.json',
          ''
        ];

      case 'pwd':
        return ['/home/<USER>/dashboard', ''];

      case 'whoami':
        return ['user', ''];

      case 'date':
        return [new Date().toString(), ''];

      case 'uptime':
        return ['System uptime: 2 days, 14 hours, 32 minutes', ''];

      case 'ps':
        return [
          'PID    COMMAND           CPU    MEM',
          '1234   dashboard-app     12.5%  45MB',
          '1235   terminal-emu      8.2%   23MB',
          '1236   system-monitor    3.1%   18MB',
          '1237   node-server       15.8%  67MB',
          ''
        ];

      case 'top':
        return [
          'CPU Usage: 34.5%',
          'Memory Usage: 68.2%',
          'Disk Usage: 45.8%',
          'Network: 125 KB/s down, 45 KB/s up',
          ''
        ];

      case 'ping':
        const host = args[0] || 'localhost';
        return [
          `PING ${host} (127.0.0.1): 56 data bytes`,
          `64 bytes from ${host}: icmp_seq=0 time=0.123ms`,
          `64 bytes from ${host}: icmp_seq=1 time=0.089ms`,
          `64 bytes from ${host}: icmp_seq=2 time=0.156ms`,
          ''
        ];

      case 'echo':
        return [args.join(' '), ''];

      case 'calc':
        try {
          const expression = args.join(' ');
          // Simple calculator - only allow basic operations for security
          const sanitized = expression.replace(/[^0-9+\-*/.() ]/g, '');
          // Use Function constructor instead of eval for better security
          const result = new Function('return ' + sanitized)();
          return [`${expression} = ${result}`, ''];
        } catch {
          return ['Error: Invalid expression', ''];
        }

      case 'weather':
        return [
          'Weather Information:',
          '🌤️  Partly Cloudy',
          '🌡️  Temperature: 22°C',
          '💨  Wind: 15 km/h',
          '💧  Humidity: 65%',
          ''
        ];

      case 'about':
        return [
          'Command Console Dashboard Terminal Emulator',
          'Version: 1.0.0',
          'Built with React and TypeScript',
          'Professional command-line interface',
          ''
        ];

      default:
        if (cmd === '') return [''];
        return [`Command not found: ${cmd}. Type 'help' for available commands.`, ''];
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentInput.trim()) return;

    const output = executeCommand(currentInput);
    const newCommand: Command = {
      input: currentInput,
      output,
      timestamp: new Date()
    };

    if (currentInput.trim().toLowerCase() === 'clear') {
      setHistory([]);
    } else {
      setHistory(prev => [...prev, newCommand]);
    }

    setCommandHistory(prev => [...prev, currentInput]);
    setCurrentInput('');
    setHistoryIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setCurrentInput(commandHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1);
          setCurrentInput('');
        } else {
          setHistoryIndex(newIndex);
          setCurrentInput(commandHistory[newIndex]);
        }
      }
    }
  };

  return (
    <div className="bg-black text-green-400 font-mono text-sm">
      <div
        ref={terminalRef}
        className="h-96 overflow-y-auto p-4 space-y-1"
        onClick={() => inputRef.current?.focus()}
      >
        {history.map((command, index) => (
          <div key={index}>
            <div className="flex items-center gap-2">
              <span className="text-blue-400">user@dashboard:~$</span>
              <span className="text-white">{command.input}</span>
            </div>
            {command.output.map((line, lineIndex) => (
              <div key={lineIndex} className="text-green-400 ml-4">
                {line}
              </div>
            ))}
          </div>
        ))}

        {/* Input Line */}
        <form onSubmit={handleSubmit} className="flex items-center gap-2">
          <span className="text-blue-400">user@dashboard:~$</span>
          <input
            ref={inputRef}
            type="text"
            value={currentInput}
            onChange={(e) => setCurrentInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1 bg-transparent border-none outline-none text-white"
            autoFocus
            spellCheck={false}
          />
        </form>
      </div>
    </div>
  );
};

export default TerminalEmulator;