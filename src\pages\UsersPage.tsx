import { ArrowLeft, Users, Settings, Activity, Bell, Lock, User, Mail, Calendar, MapPin } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';

const UsersPage = () => {
  const userProfile = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Developer',
    joinDate: 'March 2024',
    lastLogin: '2 minutes ago',
    location: 'San Francisco, CA',
    avatar: 'JD'
  };

  const activityLog = [
    { action: 'Logged into terminal', time: '2 min ago', type: 'login' },
    { action: 'Executed system diagnostics', time: '15 min ago', type: 'command' },
    { action: 'Updated profile settings', time: '1 hour ago', type: 'settings' },
    { action: 'Accessed admin panel', time: '3 hours ago', type: 'admin' },
  ];

  const stats = [
    { label: 'Commands Run', value: '1,247', icon: Activity, color: 'text-emerald-400' },
    { label: 'Session Time', value: '24h 32m', icon: Calendar, color: 'text-blue-400' },
    { label: 'Last Login', value: '2 min ago', icon: User, color: 'text-purple-400' },
    { label: 'Account Status', value: 'Active', icon: Bell, color: 'text-green-400' },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'login': return <User className="h-4 w-4 text-green-400" />;
      case 'command': return <Activity className="h-4 w-4 text-blue-400" />;
      case 'settings': return <Settings className="h-4 w-4 text-purple-400" />;
      case 'admin': return <Lock className="h-4 w-4 text-red-400" />;
      default: return <Activity className="h-4 w-4 text-slate-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 text-white">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>
      
      <div className="relative container mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Link 
              to="/" 
              className="group flex items-center justify-center w-12 h-12 rounded-xl bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 hover:border-blue-400/50 hover:bg-slate-700/50 transition-all duration-300"
            >
              <ArrowLeft className="h-5 w-5 text-blue-400 group-hover:-translate-x-0.5 transition-transform duration-200" />
            </Link>
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="absolute inset-0 bg-blue-400/20 blur-xl rounded-full"></div>
                <Users className="relative h-10 w-10 text-blue-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">User Dashboard</h1>
                <p className="text-slate-400">Account management and activity overview</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Section */}
          <div className="lg:col-span-2 space-y-6">
            {/* Profile Card */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="bg-gradient-to-r from-blue-900/20 to-slate-900/20 border-b border-slate-700/50">
                <CardTitle className="flex items-center gap-3 text-blue-400">
                  <User className="h-5 w-5" />
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex items-start gap-6">
                  <div className="relative">
                    <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white text-2xl font-bold">
                      {userProfile.avatar}
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-4 border-slate-900"></div>
                  </div>
                  <div className="flex-1 space-y-4">
                    <div>
                      <h2 className="text-2xl font-bold text-white">{userProfile.name}</h2>
                      <p className="text-blue-400 font-medium">{userProfile.role}</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                        <Mail className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-300 text-sm">{userProfile.email}</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                        <MapPin className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-300 text-sm">{userProfile.location}</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                        <Calendar className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-300 text-sm">Joined {userProfile.joinDate}</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                        <Activity className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-300 text-sm">Last login {userProfile.lastLogin}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Statistics */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="bg-gradient-to-r from-blue-900/20 to-slate-900/20 border-b border-slate-700/50">
                <CardTitle className="flex items-center gap-3 text-blue-400">
                  <Activity className="h-5 w-5" />
                  Account Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center p-4 bg-slate-800/50 rounded-lg border border-slate-700/30">
                      <stat.icon className={`h-8 w-8 mx-auto mb-2 ${stat.color}`} />
                      <div className={`text-xl font-bold ${stat.color}`}>{stat.value}</div>
                      <div className="text-slate-400 text-sm">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Activity Log */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="bg-gradient-to-r from-blue-900/20 to-slate-900/20 border-b border-slate-700/50">
                <CardTitle className="flex items-center gap-3 text-blue-400">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-slate-700/50">
                  {activityLog.map((activity, index) => (
                    <div key={index} className="p-4 hover:bg-slate-800/30 transition-colors">
                      <div className="flex items-center gap-4">
                        {getActivityIcon(activity.type)}
                        <div className="flex-1">
                          <p className="text-white text-sm font-medium">{activity.action}</p>
                          <p className="text-slate-400 text-xs">{activity.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Settings Sidebar */}
          <div className="space-y-6">
            {/* Account Settings */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-blue-400 text-lg flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Account Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <button className="w-full text-left p-3 rounded-lg bg-blue-600 hover:bg-blue-500 transition-colors text-white text-sm flex items-center gap-3">
                  <User className="h-4 w-4" />
                  Edit Profile
                </button>
                <button className="w-full text-left p-3 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-blue-400/30 transition-all text-white text-sm flex items-center gap-3">
                  <Lock className="h-4 w-4" />
                  Change Password
                </button>
                <button className="w-full text-left p-3 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-blue-400/30 transition-all text-white text-sm flex items-center gap-3">
                  <Bell className="h-4 w-4" />
                  Notifications
                </button>
                <button className="w-full text-left p-3 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-blue-400/30 transition-all text-white text-sm flex items-center gap-3">
                  <Settings className="h-4 w-4" />
                  Preferences
                </button>
              </CardContent>
            </Card>

            {/* Security Status */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-blue-400 text-lg flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  Security
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                  <span className="text-slate-300 text-sm">Two-Factor Auth</span>
                  <span className="text-green-400 text-sm font-medium">Enabled</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                  <span className="text-slate-300 text-sm">Session Timeout</span>
                  <span className="text-blue-400 text-sm font-medium">24h</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                  <span className="text-slate-300 text-sm">Last Password Change</span>
                  <span className="text-slate-400 text-sm">30 days ago</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UsersPage;
