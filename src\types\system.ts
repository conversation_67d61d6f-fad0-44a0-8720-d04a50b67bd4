// System monitoring types
export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature?: number;
    frequency?: number;
  };
  memory: {
    used: number;
    total: number;
    available: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    available: number;
    percentage: number;
    readSpeed?: number;
    writeSpeed?: number;
  };
  network: {
    downloadSpeed: number;
    uploadSpeed: number;
    totalDownload: number;
    totalUpload: number;
    connections: number;
  };
  uptime: number;
  loadAverage: number[];
  timestamp: string;
}

export interface ProcessInfo {
  pid: number;
  name: string;
  command: string;
  cpu: number;
  memory: number;
  status: 'running' | 'sleeping' | 'stopped' | 'zombie';
  user: string;
  startTime: string;
  priority: number;
}

export interface ServiceInfo {
  id: string;
  name: string;
  status: 'active' | 'inactive' | 'failed' | 'unknown';
  enabled: boolean;
  description: string;
  port?: number;
  pid?: number;
  uptime?: number;
  restartCount: number;
  lastRestart?: string;
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  service: string;
  message: string;
  metadata?: Record<string, any>;
}

export interface SystemAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  acknowledged: boolean;
  source: string;
  metadata?: Record<string, any>;
}
