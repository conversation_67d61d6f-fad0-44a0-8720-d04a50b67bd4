import { Activity, Play, Square, RotateCcw } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

const ConsolePanel = () => {
  const processes = [
    { id: 1, name: 'terminal-emu', cpu: '12.5%', memory: '45MB', status: 'running' },
    { id: 2, name: 'dashboard-app', cpu: '8.2%', memory: '23MB', status: 'running' },
    { id: 3, name: 'node-server', cpu: '15.8%', memory: '67MB', status: 'stopped' },
  ];

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-green-400">
          <Activity className="h-5 w-5" />
          Console Control
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* System Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">System Status</h4>
          <div className="bg-slate-700 p-2 rounded text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">CPU Usage:</span>
              <span className="text-white">34.5%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Memory:</span>
              <span className="text-white">68.2%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Uptime:</span>
              <span className="text-white">2d 14h</span>
            </div>
          </div>
        </div>

        {/* Running Processes */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">Processes</h4>
          {processes.map((process) => (
            <div key={process.id} className="bg-slate-700 p-2 rounded text-xs">
              <div className="flex items-center justify-between mb-1">
                <span className="text-white font-medium">{process.name}</span>
                <div className={`w-2 h-2 rounded-full ${process.status === 'running' ? 'bg-green-400' : 'bg-red-400'}`} />
              </div>
              <div className="flex justify-between text-slate-400">
                <span>CPU: {process.cpu}</span>
                <span>MEM: {process.memory}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Console Actions */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-green-400">Actions</h4>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <Play className="h-3 w-3" />
            Start Service
          </button>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <Square className="h-3 w-3" />
            Stop Service
          </button>
          <button className="w-full text-left p-2 rounded bg-slate-700 hover:bg-slate-600 transition-colors text-white text-sm flex items-center gap-2">
            <RotateCcw className="h-3 w-3" />
            Restart System
          </button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConsolePanel;