import { <PERSON>Left, Settings, Activity, Play, Square, RotateCcw, Cpu, HardDrive } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Link } from 'react-router-dom';

const ConsolePage = () => {
  const processes = [
    { id: 1, name: 'dashboard-app', pid: 1234, cpu: 12.5, memory: 45, status: 'running' },
    { id: 2, name: 'api-server', pid: 1235, cpu: 8.2, memory: 23, status: 'running' },
    { id: 3, name: 'database', pid: 1236, cpu: 15.8, memory: 67, status: 'running' },
    { id: 4, name: 'cache-service', pid: 1237, cpu: 3.1, memory: 18, status: 'stopped' },
  ];

  const getStatusColor = (status: string) => {
    return status === 'running' ? 'bg-green-400' : 'bg-red-400';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900 text-white">
      <div className="container mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link 
              to="/" 
              className="p-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors group"
            >
              <ArrowLeft className="h-5 w-5 text-purple-400 group-hover:-translate-x-1 transition-transform" />
            </Link>
            <div className="flex items-center gap-3">
              <Settings className="h-8 w-8 text-purple-400" />
              <h1 className="text-3xl font-bold text-white">System Console</h1>
            </div>
          </div>
          <p className="text-slate-300 text-lg">Monitor and control system processes</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* System Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 border-purple-400/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <Cpu className="h-8 w-8 text-purple-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">34.5%</p>
                      <p className="text-purple-300 text-sm">CPU Usage</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border-blue-400/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <HardDrive className="h-8 w-8 text-blue-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">68.2%</p>
                      <p className="text-blue-300 text-sm">Memory</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-br from-green-500/20 to-green-600/20 border-green-400/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <Activity className="h-8 w-8 text-green-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">2d 14h</p>
                      <p className="text-green-300 text-sm">Uptime</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-br from-orange-500/20 to-orange-600/20 border-orange-400/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <Play className="h-8 w-8 text-orange-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">12</p>
                      <p className="text-orange-300 text-sm">Processes</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Resource Usage */}
            <Card className="bg-slate-800/80 border-purple-400/30 shadow-2xl shadow-purple-400/10">
              <CardHeader className="bg-gradient-to-r from-purple-400/10 to-purple-500/10">
                <CardTitle className="flex items-center gap-2 text-purple-400">
                  <Activity className="h-5 w-5" />
                  Real-time Resource Monitor
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-white font-medium">CPU Usage</span>
                        <span className="text-purple-400">34.5%</span>
                      </div>
                      <Progress value={34.5} className="h-3" />
                    </div>
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-white font-medium">Memory</span>
                        <span className="text-blue-400">68.2%</span>
                      </div>
                      <Progress value={68.2} className="h-3" />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-white font-medium">Disk I/O</span>
                        <span className="text-green-400">45.8%</span>
                      </div>
                      <Progress value={45.8} className="h-3" />
                    </div>
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-white font-medium">Network</span>
                        <span className="text-orange-400">23.1%</span>
                      </div>
                      <Progress value={23.1} className="h-3" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Process List */}
            <Card className="bg-slate-800/80 border-purple-400/30">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-400">
                  <Settings className="h-5 w-5" />
                  Running Processes
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-slate-700">
                  {processes.map((process) => (
                    <div key={process.id} className="p-4 hover:bg-slate-700/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(process.status)}`} />
                          <div>
                            <h3 className="text-white font-medium">{process.name}</h3>
                            <p className="text-slate-400 text-sm">PID: {process.pid}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-6">
                          <div className="text-center">
                            <p className="text-white font-medium">{process.cpu}%</p>
                            <p className="text-slate-400 text-xs">CPU</p>
                          </div>
                          <div className="text-center">
                            <p className="text-white font-medium">{process.memory}MB</p>
                            <p className="text-slate-400 text-xs">Memory</p>
                          </div>
                          <div className="flex gap-2">
                            <button className="p-1 rounded bg-green-600 hover:bg-green-500 transition-colors">
                              <Play className="h-3 w-3 text-white" />
                            </button>
                            <button className="p-1 rounded bg-red-600 hover:bg-red-500 transition-colors">
                              <Square className="h-3 w-3 text-white" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card className="bg-slate-800/80 border-purple-400/30">
              <CardHeader className="pb-3">
                <CardTitle className="text-purple-400 text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <button className="w-full text-left p-3 rounded bg-green-600 hover:bg-green-500 transition-colors text-white text-sm flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Start Service
                </button>
                <button className="w-full text-left p-3 rounded bg-red-600 hover:bg-red-500 transition-colors text-white text-sm flex items-center gap-2">
                  <Square className="h-4 w-4" />
                  Stop Service
                </button>
                <button className="w-full text-left p-3 rounded bg-orange-600 hover:bg-orange-500 transition-colors text-white text-sm flex items-center gap-2">
                  <RotateCcw className="h-4 w-4" />
                  Restart System
                </button>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/80 border-purple-400/30">
              <CardHeader className="pb-3">
                <CardTitle className="text-purple-400 text-lg">System Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-400">Load Average:</span>
                  <span className="text-white">1.23</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Processes:</span>
                  <span className="text-white">127</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Threads:</span>
                  <span className="text-white">542</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Swap Used:</span>
                  <span className="text-white">0.2GB</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsolePage;