import { create } from 'zustand';
import type { SystemMetrics, ProcessInfo, ServiceInfo, LogEntry, SystemAlert } from '@/types/system';

interface SystemStore {
  // State
  metrics: SystemMetrics | null;
  processes: ProcessInfo[];
  services: ServiceInfo[];
  logs: LogEntry[];
  alerts: SystemAlert[];
  isConnected: boolean;
  lastUpdate: string | null;
  
  // Actions
  updateMetrics: (metrics: SystemMetrics) => void;
  updateProcesses: (processes: ProcessInfo[]) => void;
  updateServices: (services: ServiceInfo[]) => void;
  addLogEntry: (log: LogEntry) => void;
  addAlert: (alert: SystemAlert) => void;
  acknowledgeAlert: (alertId: string) => void;
  clearLogs: () => void;
  setConnectionStatus: (connected: boolean) => void;
  
  // Utilities
  getProcessById: (pid: number) => ProcessInfo | undefined;
  getServiceByName: (name: string) => ServiceInfo | undefined;
  getUnacknowledgedAlerts: () => SystemAlert[];
  getCriticalAlerts: () => SystemAlert[];
}

// Mock data generators
const generateMockMetrics = (): SystemMetrics => ({
  cpu: {
    usage: Math.random() * 100,
    cores: 8,
    temperature: 45 + Math.random() * 20,
    frequency: 2400 + Math.random() * 800,
  },
  memory: {
    used: 4096 + Math.random() * 4096,
    total: 16384,
    available: 8192 + Math.random() * 4096,
    percentage: 25 + Math.random() * 50,
  },
  disk: {
    used: 256 + Math.random() * 512,
    total: 1024,
    available: 512 + Math.random() * 256,
    percentage: 25 + Math.random() * 50,
    readSpeed: Math.random() * 100,
    writeSpeed: Math.random() * 50,
  },
  network: {
    downloadSpeed: Math.random() * 1000,
    uploadSpeed: Math.random() * 100,
    totalDownload: Math.random() * 1000000,
    totalUpload: Math.random() * 100000,
    connections: Math.floor(Math.random() * 50),
  },
  uptime: Date.now() - (Math.random() * 86400000), // Random uptime up to 24 hours
  loadAverage: [
    Math.random() * 2,
    Math.random() * 2,
    Math.random() * 2,
  ],
  timestamp: new Date().toISOString(),
});

const generateMockProcesses = (): ProcessInfo[] => [
  {
    pid: 1234,
    name: 'dashboard-app',
    command: 'node server.js',
    cpu: Math.random() * 20,
    memory: 45 + Math.random() * 20,
    status: 'running',
    user: 'app',
    startTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    priority: 0,
  },
  {
    pid: 1235,
    name: 'nginx',
    command: 'nginx: master process',
    cpu: Math.random() * 5,
    memory: 15 + Math.random() * 10,
    status: 'running',
    user: 'root',
    startTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    priority: 0,
  },
  {
    pid: 1236,
    name: 'postgres',
    command: 'postgres: main',
    cpu: Math.random() * 15,
    memory: 120 + Math.random() * 50,
    status: 'running',
    user: 'postgres',
    startTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    priority: 0,
  },
];

const generateMockServices = (): ServiceInfo[] => [
  {
    id: 'nginx',
    name: 'nginx',
    status: 'active',
    enabled: true,
    description: 'A high performance web server',
    port: 80,
    pid: 1235,
    uptime: Math.random() * 86400,
    restartCount: Math.floor(Math.random() * 5),
    lastRestart: new Date(Date.now() - Math.random() * 86400000).toISOString(),
  },
  {
    id: 'postgresql',
    name: 'postgresql',
    status: 'active',
    enabled: true,
    description: 'PostgreSQL database server',
    port: 5432,
    pid: 1236,
    uptime: Math.random() * 86400,
    restartCount: Math.floor(Math.random() * 3),
    lastRestart: new Date(Date.now() - Math.random() * 86400000).toISOString(),
  },
  {
    id: 'redis',
    name: 'redis',
    status: Math.random() > 0.8 ? 'failed' : 'active',
    enabled: true,
    description: 'Redis in-memory data store',
    port: 6379,
    uptime: Math.random() * 86400,
    restartCount: Math.floor(Math.random() * 10),
    lastRestart: new Date(Date.now() - Math.random() * 86400000).toISOString(),
  },
];

export const useSystemStore = create<SystemStore>((set, get) => ({
  // Initial state
  metrics: null,
  processes: [],
  services: [],
  logs: [],
  alerts: [],
  isConnected: false,
  lastUpdate: null,

  // Actions
  updateMetrics: (metrics: SystemMetrics) => {
    set({
      metrics,
      lastUpdate: new Date().toISOString(),
    });
  },

  updateProcesses: (processes: ProcessInfo[]) => {
    set({ processes });
  },

  updateServices: (services: ServiceInfo[]) => {
    set({ services });
  },

  addLogEntry: (log: LogEntry) => {
    set((state) => ({
      logs: [log, ...state.logs].slice(0, 1000), // Keep last 1000 logs
    }));
  },

  addAlert: (alert: SystemAlert) => {
    set((state) => ({
      alerts: [alert, ...state.alerts],
    }));
  },

  acknowledgeAlert: (alertId: string) => {
    set((state) => ({
      alerts: state.alerts.map((alert) =>
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ),
    }));
  },

  clearLogs: () => {
    set({ logs: [] });
  },

  setConnectionStatus: (connected: boolean) => {
    set({ isConnected: connected });
  },

  // Utilities
  getProcessById: (pid: number) => {
    return get().processes.find((process) => process.pid === pid);
  },

  getServiceByName: (name: string) => {
    return get().services.find((service) => service.name === name);
  },

  getUnacknowledgedAlerts: () => {
    return get().alerts.filter((alert) => !alert.acknowledged);
  },

  getCriticalAlerts: () => {
    return get().alerts.filter((alert) => alert.type === 'critical');
  },
}));

// Initialize with mock data
useSystemStore.setState({
  metrics: generateMockMetrics(),
  processes: generateMockProcesses(),
  services: generateMockServices(),
  isConnected: true,
});
