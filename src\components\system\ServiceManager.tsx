import { useState } from 'react';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Search, 
  Filter, 
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Settings
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useSystem } from '@/hooks/useSystem';
import { useAuth } from '@/hooks/useAuth';
import type { ServiceInfo } from '@/types/system';

const ServiceManager = () => {
  const { services, startService, stopService, restartService } = useSystem();
  const { checkPermission } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<keyof ServiceInfo>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const canManageServices = checkPermission('execute');

  const getStatusIcon = (status: ServiceInfo['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'inactive':
        return <Square className="h-4 w-4 text-gray-400" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-400" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
    }
  };

  const getStatusBadge = (status: ServiceInfo['status']) => {
    const variants = {
      active: 'bg-green-500/20 text-green-400 border-green-500/30',
      inactive: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
      failed: 'bg-red-500/20 text-red-400 border-red-500/30',
      unknown: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    };

    return (
      <Badge className={variants[status] || variants.unknown}>
        {status}
      </Badge>
    );
  };

  const filteredAndSortedServices = services
    .filter(service => {
      const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           service.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || service.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
      }
      
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      
      if (sortOrder === 'desc') {
        return bStr.localeCompare(aStr);
      }
      return aStr.localeCompare(bStr);
    });

  const handleServiceAction = async (action: 'start' | 'stop' | 'restart', serviceName: string) => {
    if (!canManageServices) return;
    
    try {
      switch (action) {
        case 'start':
          await startService(serviceName);
          break;
        case 'stop':
          await stopService(serviceName);
          break;
        case 'restart':
          await restartService(serviceName);
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} service:`, error);
    }
  };

  const handleSort = (column: keyof ServiceInfo) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const formatUptime = (uptime?: number) => {
    if (!uptime) return 'N/A';
    
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatLastRestart = (lastRestart?: string) => {
    if (!lastRestart) return 'Never';
    
    const date = new Date(lastRestart);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays}d ago`;
    } else if (diffHours > 0) {
      return `${diffHours}h ago`;
    } else {
      return 'Recently';
    }
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white">Service Manager</CardTitle>
        <CardDescription className="text-slate-400">
          Monitor and control system services
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-slate-700/50 border-slate-600 text-white"
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-40 bg-slate-700/50 border-slate-600 text-white">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
              <SelectItem value="unknown">Unknown</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Service Table */}
        <div className="rounded-lg border border-slate-700 overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="border-slate-700 hover:bg-slate-700/30">
                <TableHead 
                  className="text-slate-300 cursor-pointer hover:text-white"
                  onClick={() => handleSort('name')}
                >
                  Service {sortBy === 'name' && (sortOrder === 'desc' ? '↓' : '↑')}
                </TableHead>
                <TableHead className="text-slate-300">Status</TableHead>
                <TableHead className="text-slate-300">Port</TableHead>
                <TableHead className="text-slate-300">Uptime</TableHead>
                <TableHead 
                  className="text-slate-300 cursor-pointer hover:text-white"
                  onClick={() => handleSort('restartCount')}
                >
                  Restarts {sortBy === 'restartCount' && (sortOrder === 'desc' ? '↓' : '↑')}
                </TableHead>
                <TableHead className="text-slate-300">Last Restart</TableHead>
                {canManageServices && <TableHead className="text-slate-300">Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedServices.map((service) => (
                <TableRow key={service.id} className="border-slate-700 hover:bg-slate-700/20">
                  <TableCell>
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        <span className="text-white font-medium">{service.name}</span>
                        {service.enabled && (
                          <Badge variant="outline" className="text-xs border-blue-500/30 text-blue-400">
                            enabled
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-slate-400 mt-1">
                        {service.description}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(service.status)}
                      {getStatusBadge(service.status)}
                    </div>
                  </TableCell>
                  <TableCell className="text-slate-300">
                    {service.port ? (
                      <Badge variant="outline" className="border-emerald-500/30 text-emerald-400">
                        :{service.port}
                      </Badge>
                    ) : (
                      <span className="text-slate-500">N/A</span>
                    )}
                  </TableCell>
                  <TableCell className="text-slate-300">
                    {formatUptime(service.uptime)}
                  </TableCell>
                  <TableCell className="text-slate-300">
                    <div className="flex items-center gap-2">
                      <span>{service.restartCount}</span>
                      {service.restartCount > 5 && (
                        <AlertTriangle className="h-4 w-4 text-yellow-400" title="High restart count" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-slate-400 text-sm">
                    {formatLastRestart(service.lastRestart)}
                  </TableCell>
                  {canManageServices && (
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                          {service.status !== 'active' && (
                            <DropdownMenuItem
                              onClick={() => handleServiceAction('start', service.name)}
                              className="text-green-400 hover:text-green-300 hover:bg-green-500/10"
                            >
                              <Play className="mr-2 h-4 w-4" />
                              Start
                            </DropdownMenuItem>
                          )}
                          {service.status === 'active' && (
                            <DropdownMenuItem
                              onClick={() => handleServiceAction('stop', service.name)}
                              className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                            >
                              <Square className="mr-2 h-4 w-4" />
                              Stop
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem
                            onClick={() => handleServiceAction('restart', service.name)}
                            className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                          >
                            <RotateCcw className="mr-2 h-4 w-4" />
                            Restart
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-slate-700" />
                          <DropdownMenuItem className="text-slate-300 hover:text-white hover:bg-slate-700">
                            <Settings className="mr-2 h-4 w-4" />
                            Configure
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {filteredAndSortedServices.length === 0 && (
          <div className="text-center py-8 text-slate-400">
            No services found matching your criteria.
          </div>
        )}

        {/* Summary */}
        <div className="flex flex-wrap gap-4 text-sm text-slate-400">
          <span>Total: {services.length} services</span>
          <span>Active: {services.filter(s => s.status === 'active').length}</span>
          <span>Inactive: {services.filter(s => s.status === 'inactive').length}</span>
          <span>Failed: {services.filter(s => s.status === 'failed').length}</span>
          <span>Enabled: {services.filter(s => s.enabled).length}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default ServiceManager;
