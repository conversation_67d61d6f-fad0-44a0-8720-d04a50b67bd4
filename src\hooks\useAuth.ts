import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/authStore';
import { authApi } from '@/services/api';
import { wsService } from '@/services/websocket';
import { toast } from '@/hooks/use-toast';
import type { LoginCredentials } from '@/types/auth';

export const useAuth = () => {
  const navigate = useNavigate();
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login: storeLogin,
    logout: storeLogout,
    refreshToken: storeRefreshToken,
    updateUser,
    clearError,
    hasPermission,
    isRole,
  } = useAuthStore();

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      clearError();
      await storeLogin(credentials);
      
      // Connect to WebSocket after successful login
      try {
        await wsService.connect();
      } catch (wsError) {
        console.warn('Failed to connect to WebSocket:', wsError);
        // Don't fail login if WebSocket connection fails
      }
      
      toast({
        title: 'Login Successful',
        description: `Welcome back, ${credentials.username}!`,
      });
      
      navigate('/');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      toast({
        title: 'Login Failed',
        description: message,
        variant: 'destructive',
      });
      throw error;
    }
  }, [storeLogin, clearError, navigate]);

  const logout = useCallback(async () => {
    try {
      // Disconnect WebSocket
      wsService.disconnect();
      
      // Call API logout (optional, for server-side cleanup)
      try {
        await authApi.logout();
      } catch (error) {
        console.warn('API logout failed:', error);
        // Continue with local logout even if API call fails
      }
      
      storeLogout();
      
      toast({
        title: 'Logged Out',
        description: 'You have been successfully logged out.',
      });
      
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if there's an error
      storeLogout();
      navigate('/login');
    }
  }, [storeLogout, navigate]);

  const refreshToken = useCallback(async () => {
    try {
      await storeRefreshToken();
    } catch (error) {
      console.error('Token refresh failed:', error);
      // If refresh fails, logout user
      logout();
      throw error;
    }
  }, [storeRefreshToken, logout]);

  const checkPermission = useCallback((permission: string): boolean => {
    return hasPermission(permission);
  }, [hasPermission]);

  const checkRole = useCallback((role: string): boolean => {
    return isRole(role);
  }, [isRole]);

  const requireAuth = useCallback(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return false;
    }
    return true;
  }, [isAuthenticated, navigate]);

  const requirePermission = useCallback((permission: string) => {
    if (!requireAuth()) return false;
    
    if (!hasPermission(permission)) {
      toast({
        title: 'Access Denied',
        description: `You don't have permission to ${permission}`,
        variant: 'destructive',
      });
      navigate('/');
      return false;
    }
    return true;
  }, [requireAuth, hasPermission, navigate]);

  const requireRole = useCallback((role: string) => {
    if (!requireAuth()) return false;
    
    if (!isRole(role)) {
      toast({
        title: 'Access Denied',
        description: `This feature requires ${role} role`,
        variant: 'destructive',
      });
      navigate('/');
      return false;
    }
    return true;
  }, [requireAuth, isRole, navigate]);

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    
    // Actions
    login,
    logout,
    refreshToken,
    updateUser,
    clearError,
    
    // Utilities
    checkPermission,
    checkRole,
    requireAuth,
    requirePermission,
    requireRole,
  };
};
