import type { WebSocketMessage, WebSocketEvent } from '@/types/api';
import type { SystemMetrics, ProcessInfo, ServiceInfo, LogEntry, SystemAlert } from '@/types/system';
import { useSystemStore } from '@/stores/systemStore';

type EventHandler = (data: any) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventHandlers: Map<string, EventHandler[]> = new Map();
  private isConnecting = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor(url: string = 'ws://localhost:3001/ws') {
    this.url = url;
    this.setupDefaultHandlers();
  }

  private setupDefaultHandlers() {
    // Set up default handlers for system store updates
    this.on('system_metrics', (metrics: SystemMetrics) => {
      useSystemStore.getState().updateMetrics(metrics);
    });

    this.on('process_update', (processes: ProcessInfo[]) => {
      useSystemStore.getState().updateProcesses(processes);
    });

    this.on('service_status', (services: ServiceInfo[]) => {
      useSystemStore.getState().updateServices(services);
    });

    this.on('log_entry', (log: LogEntry) => {
      useSystemStore.getState().addLogEntry(log);
    });

    this.on('alert', (alert: SystemAlert) => {
      useSystemStore.getState().addAlert(alert);
    });
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Connection already in progress'));
        return;
      }

      this.isConnecting = true;

      try {
        // Get auth token for WebSocket connection
        const token = localStorage.getItem('auth-storage');
        const authData = token ? JSON.parse(token) : null;
        const wsUrl = authData?.state?.token 
          ? `${this.url}?token=${authData.state.token}`
          : this.url;

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          useSystemStore.getState().setConnectionStatus(true);
          this.startHeartbeat();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          useSystemStore.getState().setConnectionStatus(false);
          this.stopHeartbeat();
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          useSystemStore.getState().setConnectionStatus(false);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.stopHeartbeat();
    useSystemStore.getState().setConnectionStatus(false);
  }

  private scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect().catch(console.error);
    }, delay);
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send('ping', {});
      }
    }, 30000); // Send ping every 30 seconds
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private handleMessage(message: WebSocketMessage) {
    const handlers = this.eventHandlers.get(message.type) || [];
    handlers.forEach(handler => {
      try {
        handler(message.payload);
      } catch (error) {
        console.error(`Error in WebSocket handler for ${message.type}:`, error);
      }
    });
  }

  send(type: string, payload: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date().toISOString(),
        id: Math.random().toString(36).substr(2, 9),
      };
      
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  on(eventType: string, handler: EventHandler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  off(eventType: string, handler: EventHandler) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

// Create and export WebSocket service instance
export const wsService = new WebSocketService();

// Mock data simulation for development
let mockInterval: NodeJS.Timeout | null = null;

export const startMockDataSimulation = () => {
  if (mockInterval) return;

  mockInterval = setInterval(() => {
    const systemStore = useSystemStore.getState();
    
    // Simulate system metrics updates
    const mockMetrics = {
      cpu: {
        usage: Math.random() * 100,
        cores: 8,
        temperature: 45 + Math.random() * 20,
        frequency: 2400 + Math.random() * 800,
      },
      memory: {
        used: 4096 + Math.random() * 4096,
        total: 16384,
        available: 8192 + Math.random() * 4096,
        percentage: 25 + Math.random() * 50,
      },
      disk: {
        used: 256 + Math.random() * 512,
        total: 1024,
        available: 512 + Math.random() * 256,
        percentage: 25 + Math.random() * 50,
        readSpeed: Math.random() * 100,
        writeSpeed: Math.random() * 50,
      },
      network: {
        downloadSpeed: Math.random() * 1000,
        uploadSpeed: Math.random() * 100,
        totalDownload: Math.random() * 1000000,
        totalUpload: Math.random() * 100000,
        connections: Math.floor(Math.random() * 50),
      },
      uptime: Date.now() - (Math.random() * 86400000),
      loadAverage: [Math.random() * 2, Math.random() * 2, Math.random() * 2],
      timestamp: new Date().toISOString(),
    };

    systemStore.updateMetrics(mockMetrics);

    // Occasionally add a log entry
    if (Math.random() < 0.3) {
      const logLevels = ['info', 'warn', 'error', 'debug'] as const;
      const services = ['nginx', 'postgresql', 'redis', 'dashboard-app'];
      const messages = [
        'Service started successfully',
        'Connection established',
        'Request processed',
        'Cache miss',
        'Database query executed',
        'User authenticated',
        'Configuration reloaded',
      ];

      const mockLog: LogEntry = {
        id: Math.random().toString(36).substr(2, 9),
        timestamp: new Date().toISOString(),
        level: logLevels[Math.floor(Math.random() * logLevels.length)],
        service: services[Math.floor(Math.random() * services.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        metadata: { requestId: Math.random().toString(36).substr(2, 9) },
      };

      systemStore.addLogEntry(mockLog);
    }

    // Occasionally add an alert
    if (Math.random() < 0.1) {
      const alertTypes = ['critical', 'warning', 'info'] as const;
      const titles = ['High CPU Usage', 'Memory Warning', 'Service Down', 'Disk Space Low'];
      const messages = [
        'CPU usage has exceeded 90% for the last 5 minutes',
        'Memory usage is approaching critical levels',
        'Redis service has stopped responding',
        'Available disk space is below 10%',
      ];

      const mockAlert: SystemAlert = {
        id: Math.random().toString(36).substr(2, 9),
        type: alertTypes[Math.floor(Math.random() * alertTypes.length)],
        title: titles[Math.floor(Math.random() * titles.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        timestamp: new Date().toISOString(),
        acknowledged: false,
        source: 'system-monitor',
      };

      systemStore.addAlert(mockAlert);
    }
  }, 2000); // Update every 2 seconds
};

export const stopMockDataSimulation = () => {
  if (mockInterval) {
    clearInterval(mockInterval);
    mockInterval = null;
  }
};
