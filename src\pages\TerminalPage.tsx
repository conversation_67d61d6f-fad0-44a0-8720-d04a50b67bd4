import { ArrowLeft, Terminal, Activity, Cpu, HardDrive, Wifi } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import EnhancedTerminal from '@/components/terminal/EnhancedTerminal';

const TerminalPage = () => {
  const systemStats = [
    { label: 'CPU Usage', value: '34%', icon: Cpu, color: 'text-emerald-400' },
    { label: 'Memory', value: '68%', icon: Activity, color: 'text-blue-400' },
    { label: 'Storage', value: '45%', icon: HardDrive, color: 'text-purple-400' },
    { label: 'Network', value: '125KB/s', icon: Wifi, color: 'text-yellow-400' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 text-white">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-emerald-900/20 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>

      <div className="relative container mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Link
              to="/"
              className="group flex items-center justify-center w-12 h-12 rounded-xl bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 hover:border-emerald-400/50 hover:bg-slate-700/50 transition-all duration-300"
            >
              <ArrowLeft className="h-5 w-5 text-emerald-400 group-hover:-translate-x-0.5 transition-transform duration-200" />
            </Link>
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="absolute inset-0 bg-emerald-400/20 blur-xl rounded-full"></div>
                <Terminal className="relative h-10 w-10 text-emerald-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">Terminal Console</h1>
                <p className="text-slate-400">Advanced command-line interface</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Terminal - Main Area */}
          <div className="lg:col-span-3">
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50 shadow-2xl">
              <CardHeader className="pb-3 bg-gradient-to-r from-emerald-900/20 to-slate-900/20 border-b border-slate-700/50">
                <CardTitle className="flex items-center gap-3 text-emerald-400">
                  <Terminal className="h-5 w-5" />
                  Interactive Terminal
                  <div className="ml-auto flex gap-1">
                    <div className="w-3 h-3 rounded-full bg-red-400"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                    <div className="w-3 h-3 rounded-full bg-green-400"></div>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <EnhancedTerminal />
              </CardContent>
            </Card>
          </div>

          {/* Status Panel */}
          <div className="space-y-6">
            {/* System Stats */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-emerald-400 text-lg">
                  <Activity className="h-5 w-5" />
                  System Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {systemStats.map((stat, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <div className="flex items-center gap-3">
                      <stat.icon className={`h-4 w-4 ${stat.color}`} />
                      <span className="text-slate-300 text-sm">{stat.label}</span>
                    </div>
                    <span className={`font-mono font-medium ${stat.color}`}>{stat.value}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Terminal Info */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-emerald-400 text-lg">Session Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Status:</span>
                  <span className="text-emerald-400 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-emerald-400"></div>
                    Active
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Shell:</span>
                  <span className="text-white font-mono">bash</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Commands:</span>
                  <span className="text-white font-mono">15</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Uptime:</span>
                  <span className="text-white font-mono">2h 34m</span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Commands */}
            <Card className="bg-slate-900/80 backdrop-blur-sm border-slate-700/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-emerald-400 text-lg">Quick Commands</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {['help - Show commands', 'ls - List files', 'top - System info', 'clear - Clear screen'].map((cmd, index) => (
                  <button
                    key={index}
                    className="w-full text-left p-3 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-emerald-400/30 transition-all duration-200 text-white text-sm font-mono"
                  >
                    {cmd}
                  </button>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TerminalPage;