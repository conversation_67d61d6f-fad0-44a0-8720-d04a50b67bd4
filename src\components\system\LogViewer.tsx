import { useState, useEffect, useRef } from 'react';
import { 
  Search, 
  Filter, 
  Download, 
  Trash2, 
  Play, 
  Pause,
  AlertCircle,
  Info,
  AlertTriangle,
  Bug,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useSystem } from '@/hooks/useSystem';
import { useAuth } from '@/hooks/useAuth';
import type { LogEntry } from '@/types/system';

const LogViewer = () => {
  const { logs, clearLogs } = useSystem();
  const { checkPermission } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [serviceFilter, setServiceFilter] = useState<string>('all');
  const [autoScroll, setAutoScroll] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const logContainerRef = useRef<HTMLDivElement>(null);

  const canManageLogs = checkPermission('write');

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && !isPaused && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs, autoScroll, isPaused]);

  const getLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      case 'warn':
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-400" />;
      case 'debug':
        return <Bug className="h-4 w-4 text-gray-400" />;
      default:
        return <Info className="h-4 w-4 text-blue-400" />;
    }
  };

  const getLevelBadge = (level: LogEntry['level']) => {
    const variants = {
      error: 'bg-red-500/20 text-red-400 border-red-500/30',
      warn: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      info: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      debug: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
    };

    return (
      <Badge className={`${variants[level]} text-xs`}>
        {level.toUpperCase()}
      </Badge>
    );
  };

  const getServiceBadge = (service: string) => {
    const colors = [
      'bg-emerald-500/20 text-emerald-400 border-emerald-500/30',
      'bg-purple-500/20 text-purple-400 border-purple-500/30',
      'bg-orange-500/20 text-orange-400 border-orange-500/30',
      'bg-pink-500/20 text-pink-400 border-pink-500/30',
      'bg-cyan-500/20 text-cyan-400 border-cyan-500/30',
    ];
    
    const colorIndex = service.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
    
    return (
      <Badge className={`${colors[colorIndex]} text-xs`}>
        {service}
      </Badge>
    );
  };

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.service.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = levelFilter === 'all' || log.level === levelFilter;
    const matchesService = serviceFilter === 'all' || log.service === serviceFilter;
    return matchesSearch && matchesLevel && matchesService;
  });

  const uniqueServices = Array.from(new Set(logs.map(log => log.service))).sort();

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  const handleClearLogs = () => {
    if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
      clearLogs();
    }
  };

  const handleExportLogs = () => {
    const logData = filteredLogs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      service: log.service,
      message: log.message,
      metadata: log.metadata
    }));

    const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-white">System Logs</CardTitle>
            <CardDescription className="text-slate-400">
              Real-time system log monitoring and analysis
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsPaused(!isPaused)}
              className={isPaused ? 'text-green-400 hover:text-green-300' : 'text-yellow-400 hover:text-yellow-300'}
            >
              {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
              {isPaused ? 'Resume' : 'Pause'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Controls */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-slate-700/50 border-slate-600 text-white"
            />
          </div>
          
          <div className="flex gap-2">
            <Select value={levelFilter} onValueChange={setLevelFilter}>
              <SelectTrigger className="w-32 bg-slate-700/50 border-slate-600 text-white">
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="warn">Warning</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="debug">Debug</SelectItem>
              </SelectContent>
            </Select>

            <Select value={serviceFilter} onValueChange={setServiceFilter}>
              <SelectTrigger className="w-40 bg-slate-700/50 border-slate-600 text-white">
                <SelectValue placeholder="Service" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="all">All Services</SelectItem>
                {uniqueServices.map(service => (
                  <SelectItem key={service} value={service}>{service}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="auto-scroll"
                checked={autoScroll}
                onCheckedChange={setAutoScroll}
              />
              <Label htmlFor="auto-scroll" className="text-sm text-slate-300">
                Auto-scroll
              </Label>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExportLogs}
              className="border-slate-600 text-slate-300 hover:text-white"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>

            {canManageLogs && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearLogs}
                className="border-red-600 text-red-400 hover:text-red-300 hover:bg-red-500/10"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Log Display */}
        <div 
          ref={logContainerRef}
          className="bg-slate-900/50 rounded-lg border border-slate-700 h-96 overflow-y-auto font-mono text-sm"
        >
          {filteredLogs.length === 0 ? (
            <div className="flex items-center justify-center h-full text-slate-400">
              No logs found matching your criteria.
            </div>
          ) : (
            <div className="p-4 space-y-2">
              {filteredLogs.map((log) => (
                <div 
                  key={log.id} 
                  className="flex items-start gap-3 p-2 rounded hover:bg-slate-800/30 transition-colors"
                >
                  <div className="flex items-center gap-2 min-w-0 flex-shrink-0">
                    <Clock className="h-3 w-3 text-slate-500" />
                    <span className="text-slate-400 text-xs">
                      {formatTimestamp(log.timestamp)}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {getLevelIcon(log.level)}
                    {getLevelBadge(log.level)}
                  </div>
                  
                  <div className="flex-shrink-0">
                    {getServiceBadge(log.service)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <span className="text-slate-200 break-words">
                      {log.message}
                    </span>
                    {log.metadata && Object.keys(log.metadata).length > 0 && (
                      <div className="mt-1 text-xs text-slate-500">
                        {Object.entries(log.metadata).map(([key, value]) => (
                          <span key={key} className="mr-3">
                            {key}: {String(value)}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Statistics */}
        <div className="flex flex-wrap gap-4 text-sm text-slate-400 border-t border-slate-700 pt-4">
          <span>Total: {logs.length} entries</span>
          <span>Filtered: {filteredLogs.length} entries</span>
          <span>Errors: {logs.filter(l => l.level === 'error').length}</span>
          <span>Warnings: {logs.filter(l => l.level === 'warn').length}</span>
          <span>Info: {logs.filter(l => l.level === 'info').length}</span>
          <span>Debug: {logs.filter(l => l.level === 'debug').length}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default LogViewer;
